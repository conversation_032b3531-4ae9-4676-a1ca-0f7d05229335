package com.iflytek.lynxiao.support.consts;

import lombok.Getter;

/**
 * 流程类型
 *
 * <AUTHOR>  2024/10/29 13:51
 */
@Getter
public enum FlowType {

    /**
     * 场景策略
     */
    scene(-1),


    /**
     * 产品方案
     */
    product(-2),


    /**
     * 流程模版
     */
    template(-3),


    /**
     * 产品分流
     */
    product_traffic(-4),

    /**
     * 产品方案上线计划
     */
    product_online(-5),

    /**
     * 产品分流-验证环境
     */
    product_traffic_verify(-6),

    /**
     * 归因分析
     */
    eval(-7),

    ;


    private final int code;

    FlowType(int code) {
        this.code = code;
    }
}
