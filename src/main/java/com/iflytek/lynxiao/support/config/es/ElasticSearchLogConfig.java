package com.iflytek.lynxiao.support.config.es;

import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.List;

@Log4j2
@Configuration
public class ElasticSearchLogConfig {

    @Bean
    @Primary
    @ConfigurationProperties(prefix = "lynxiao.log.hf.elasticsearch")
    public ElasticsearchProperties hfElasticsearchProperties() {
        return new ElasticsearchProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "lynxiao.log.sh.elasticsearch")
    public ElasticsearchProperties shElasticsearchProperties() {
        return new ElasticsearchProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "lynxiao.log.dx.elasticsearch")
    public ElasticsearchProperties dxElasticsearchProperties() {
        return new ElasticsearchProperties();
    }

    @Primary
    @Bean
    @Qualifier("hfLogRestHighLevelClient")
    public RestHighLevelClient hfLogRestHighLevelClient(@Qualifier("hfElasticsearchProperties") ElasticsearchProperties hfElasticsearchProperties) {
        return createRestHighLevelClient(hfElasticsearchProperties);
    }

    @Bean
    @Qualifier("shLogRestHighLevelClient")
    public RestHighLevelClient shLogRestHighLevelClient(@Qualifier("shElasticsearchProperties") ElasticsearchProperties shElasticsearchProperties) {
        return createRestHighLevelClient(shElasticsearchProperties);
    }

    @Bean
    @Qualifier("dxLogRestHighLevelClient")
    public RestHighLevelClient dxLogRestHighLevelClient(@Qualifier("dxElasticsearchProperties") ElasticsearchProperties shElasticsearchProperties) {
        return createRestHighLevelClient(shElasticsearchProperties);
    }

    private RestHighLevelClient createRestHighLevelClient(ElasticsearchProperties properties) {
        BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(
                AuthScope.ANY,
                new UsernamePasswordCredentials(properties.getUsername(), properties.getPassword())
        );

        List<HttpHost> hosts = properties.getUris().stream()
                .map(HttpHost::create)
                .toList();

        RestClientBuilder builder = RestClient.builder(hosts.toArray(new HttpHost[0]))
                .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider));

        if (properties.getConnectionTimeout() != null) {
            builder.setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                    .setConnectTimeout(Math.toIntExact(properties.getConnectionTimeout().toMillis()))
                    .setSocketTimeout(Math.toIntExact(properties.getSocketTimeout().toMillis())));
        }


        return new RestHighLevelClient(builder);
    }
}
