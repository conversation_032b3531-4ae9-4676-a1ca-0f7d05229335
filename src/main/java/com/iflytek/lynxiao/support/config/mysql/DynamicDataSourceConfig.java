package com.iflytek.lynxiao.support.config.mysql;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

import jakarta.persistence.EntityManagerFactory;
import javax.sql.DataSource;

/**
 * 动态数据源配置类
 */
@Configuration
public class DynamicDataSourceConfig {


    @Primary
    @Bean
    @ConfigurationProperties(prefix = "cluster.mysql.support")
    public DataSourceProperties supportDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean
    @ConfigurationProperties(prefix = "cluster.mysql.portal")
    public DataSourceProperties portalDataSourceProperties() {
        return new DataSourceProperties();
    }


    @Bean
    @Primary
    public DataSource supportDataSource() {
        return supportDataSourceProperties().initializeDataSourceBuilder().build();
    }


    @Bean
    public DataSource portalDataSource() {
        return portalDataSourceProperties().initializeDataSourceBuilder().build();
    }


    @Primary
    @Bean(name = "supportEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean supportEntityManagerFactory(@Qualifier("supportDataSource") DataSource supportDataSource) {
        return createEntityManagerFactory(supportDataSource, "support","com.iflytek.lynxiao.support.repository.support.entity");
    }


    @Bean(name = "portalEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean portalEntityManagerFactory(@Qualifier("portalDataSource") DataSource portalDataSource) {
        return createEntityManagerFactory(portalDataSource, "portal","com.iflytek.lynxiao.support.repository.portal.entity");
    }

    private LocalContainerEntityManagerFactoryBean createEntityManagerFactory(DataSource dataSource,String persistenceUnitName, String packagesToScan) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan(packagesToScan);
        em.setPersistenceUnitName(persistenceUnitName); // 设置持久化单元名称
        // 设置 JPA 提供者适配器
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        return em;
    }

    @Bean(name = "supportTransactionManager")
    @Primary
    public JpaTransactionManager supportTransactionManager(@Qualifier("supportEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }

    @Bean(name = "portalTransactionManager")
    public JpaTransactionManager portalTransactionManager(@Qualifier("portalEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }


    // 定义JPA仓库配置
    @EnableJpaRepositories(
            basePackages = "com.iflytek.lynxiao.support.repository.support",
            entityManagerFactoryRef = "supportEntityManagerFactory",
            transactionManagerRef = "supportTransactionManager"
    )
    public static class SupportRepositoriesConfig {}

    @EnableJpaRepositories(
            basePackages = "com.iflytek.lynxiao.support.repository.portal",
            entityManagerFactoryRef = "portalEntityManagerFactory",
            transactionManagerRef = "portalTransactionManager"
    )
    public static class PortalRegionRepositoriesConfig {}



}
