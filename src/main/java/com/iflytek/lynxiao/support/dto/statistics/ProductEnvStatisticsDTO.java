package com.iflytek.lynxiao.support.dto.statistics;


import com.iflytek.lynxiao.support.util.DataCalculateUtil;
import com.iflytek.lynxiao.support.util.excel.MyExcelCollection;
import com.iflytek.lynxiao.support.util.excel.MyExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 按环境日期段统计产品方案流量明细
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProductEnvStatisticsDTO {
    @MyExcelProperty(name = "日期")
    private String date;



    @MyExcelCollection(name = "业务应用")
    private List<App> details;



    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class App {
        @MyExcelProperty(name = "业务应用")
        private String  app;


        @MyExcelCollection(name = "产品方案")
        private List<Product> details;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class Product {
        @MyExcelProperty(name = "产品方案")
        private String  product;


        @MyExcelCollection(name = "产品方案版本")
        private List<ProductVersion> details;

    }

    @Data
    public static class ProductVersion {
        @MyExcelProperty(name = "方案版本")
        private String  productVersion;

        @MyExcelCollection(name = "环境")
        private List<Region> details;


    }

    @Data
    public static class Region {

        @MyExcelProperty(name = "环境")
        private String region;

        @MyExcelProperty(name = "请求总数")
        private long requestCount;

        @MyExcelProperty(name = "有结果数")
        private long hasResultCount;

        @MyExcelProperty(name = "无结果数")
        private long noResultCount;

        @MyExcelProperty(name = "有结果占比")
        private BigDecimal hasResultPercentage;

        @MyExcelProperty(name = "流量占比")
        private BigDecimal trafficPercentage;
    }


    /**
     * 转换按环境日期段统计产品方案流量明细
     *
     * @param tileList List<StatisticsTileDTO>
     * @return List<ProductEnvStatisticsDTO>
     */
    public static List<ProductEnvStatisticsDTO> convertProductEnv(List<StatisticsTileDTO> tileList) {
        // 第一层：按date分组
        Map<String, List<StatisticsTileDTO>> dateGroup = tileList.stream()
                .collect(Collectors.groupingBy(StatisticsTileDTO::getDate))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        return dateGroup.entrySet().stream().map(dateEntry -> {
            ProductEnvStatisticsDTO dto = new ProductEnvStatisticsDTO();
            dto.setDate(dateEntry.getKey());

            long totalCount = dateEntry.getValue().stream().mapToLong(StatisticsTileDTO::getRequestCount).sum();

            // 第二层：按app分组
            Map<String, List<StatisticsTileDTO>> appGroup = dateEntry.getValue().stream()
                    .collect(Collectors.groupingBy(StatisticsTileDTO::getApp));

            dto.setDetails(appGroup.entrySet().stream().map(appEntry -> {
                ProductEnvStatisticsDTO.App app = new ProductEnvStatisticsDTO.App();
                app.setApp(appEntry.getKey());

                // 第三层：按product分组
                Map<String, List<StatisticsTileDTO>> productGroup = appEntry.getValue().stream()
                        .collect(Collectors.groupingBy(StatisticsTileDTO::getProduct));

                app.setDetails(productGroup.entrySet().stream().map(productEntry -> {
                    ProductEnvStatisticsDTO.Product product = new ProductEnvStatisticsDTO.Product();
                    product.setProduct(productEntry.getKey());

                    // 第四层：按productVersion分组
                    Map<String, List<StatisticsTileDTO>> versionGroup = productEntry.getValue().stream()
                            .collect(Collectors.groupingBy(StatisticsTileDTO::getProductVersion));

                    product.setDetails(versionGroup.entrySet().stream().map(versionEntry -> {
                        ProductEnvStatisticsDTO.ProductVersion pv = new ProductEnvStatisticsDTO.ProductVersion();
                        pv.setProductVersion(versionEntry.getKey());

                        // 第五层：按region分组合并统计值
                        Map<String, List<StatisticsTileDTO>> regionGroup = versionEntry.getValue().stream()
                                .collect(Collectors.groupingBy(StatisticsTileDTO::getRegion));

                        pv.setDetails(regionGroup.entrySet().stream().map(regionEntry -> {
                            List<StatisticsTileDTO> items = regionEntry.getValue();
                            ProductEnvStatisticsDTO.Region region = new ProductEnvStatisticsDTO.Region();
                            region.setRegion(regionEntry.getKey());

                            // 合并数值字段
                            long totalRequest = items.stream().mapToLong(StatisticsTileDTO::getRequestCount).sum();
                            long hasResult = items.stream().mapToLong(StatisticsTileDTO::getHasResultCount).sum();
                            region.setRequestCount(totalRequest);
                            region.setHasResultCount(hasResult);
                            region.setNoResultCount(totalRequest - hasResult);

                            // 计算百分比
                            region.setHasResultPercentage(DataCalculateUtil.calculatePercentage(hasResult, totalRequest));
                            return region;
                        }).collect(Collectors.toList()));

                        // 计算流量占比
                        pv.getDetails().forEach(region -> {
                            region.setTrafficPercentage(DataCalculateUtil.calculatePercentage(region.getRequestCount(), totalCount));
                        });
                        return pv;
                    }).collect(Collectors.toList()));
                    return product;
                }).collect(Collectors.toList()));
                return app;
            }).collect(Collectors.toList()));
            return dto;
        }).collect(Collectors.toList());
    }
}
