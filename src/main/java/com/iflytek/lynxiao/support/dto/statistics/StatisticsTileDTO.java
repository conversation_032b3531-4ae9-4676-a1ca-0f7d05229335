package com.iflytek.lynxiao.support.dto.statistics;

import lombok.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 平台流量统计-（平铺结构）
 * <AUTHOR>
 */
@Setter
@Getter
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsTileDTO {

    private String date;

    private String region;

    private String app;

    private String product;

    private String productVersion;

    private long requestCount;

    private long hasResultCount;

    private long noResultCount;

    private BigDecimal hasResultPercentage;

    private BigDecimal trafficPercentage;

    /**
     * 埋点项
     */
    private String burialItem;

    private Map<String, Object> burialItemMap;
}
