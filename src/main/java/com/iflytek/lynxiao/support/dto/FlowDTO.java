package com.iflytek.lynxiao.support.dto;

import com.iflytek.lynxiao.support.dto.common.AuditingDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>  2024/9/26 14:08
 */
@Getter
@Setter
public class FlowDTO extends AuditingDTO<String> {

    /**
     * 编码
     */
    @Schema(title = "编码")
    private String code;

    /**
     * 名称
     */
    @Schema(title = "名称")
    private String name;

    /**
     * 描述
     */
    @Schema(title = "描述")
    private String description;

    /**
     * 分组
     */
    @Schema(title = "分组")
    private String groupName;

    /**
     * 流程类型，1：场景策略；2：产品方案
     */
    @Schema(title = "流程类型，1：场景策略；2：产品方案")
    private Integer type;

    /**
     * 区域编码
     */
    @Schema(title = "区域编码")
    private String region;

    /**
     * 是否可见，默认不可见
     */
    @Schema(title = "是否可见，默认不可见")
    private Boolean opened;
}
