package com.iflytek.lynxiao.support.dto.statistics;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Comparator;

/**
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class FlowAnalysisMailDTO {

    /**
     * 日期
     */
    private String date;
    /**
     * 环境
     */
    private String region;
    /**
     * 业务code
     */
    private String bizCode;
    /**
     * 业务id
     */
    private String bizId;
    /**
     * 请求总量
     */
    private Long requestCount;
    /**
     * 有结果数量
     */
    private Long hasResult;
    /**
     * 无结果数量
     */
    private Long noResult;
    /**
     * 有结果占比
     */
    private BigDecimal hasResultPercentage;
    /**
     * 流量占比
     */
    private BigDecimal trafficPercentage;
    /**
     * 业务模块
     */
    private String module;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志
     */
    private Boolean deleted;

    // 比较器，用于比较 trafficPercentage
    public static Comparator<FlowAnalysisMailDTO> trafficPercentageComparator = Comparator
            .comparing(FlowAnalysisMailDTO::getTrafficPercentage)
            .reversed(); // 降序排序
}
