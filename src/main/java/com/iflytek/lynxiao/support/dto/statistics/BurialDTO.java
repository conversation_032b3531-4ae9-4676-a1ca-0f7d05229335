package com.iflytek.lynxiao.support.dto.statistics;

import com.iflytek.lynxiao.support.dto.common.GeneratedMetaDict;
import com.iflytek.lynxiao.support.util.DataCalculateUtil;
import com.iflytek.lynxiao.support.util.excel.MyExcelCollection;
import com.iflytek.lynxiao.support.util.excel.MyExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 * 埋点导出实体
 */
@Data
public class BurialDTO {

    @MyExcelProperty(name = "日期")
    private String date;

    @MyExcelCollection(name = "列表")
    private List<Product> details;

    @Data
    public static class Product {

        @MyExcelProperty(name = "产品方案")
        private String product;

        @MyExcelCollection(name = "埋点列表")
        private List<Burial> details;

    }

    @Data
    public static class Burial {

        @MyExcelProperty(name = "埋点项")
        private String burialItem;

        @MyExcelProperty(name = "请求总数")
        private long requestCount;

        @MyExcelProperty(name = "有结果数")
        private long hasResultCount;

        @MyExcelProperty(name = "无结果数")
        private long noResultCount;

        @MyExcelProperty(name = "有结果占比")
        private BigDecimal hasResultPercentage;

        @MyExcelProperty(name = "流量占比")
        private BigDecimal trafficPercentage;

        private Map<String, Object> burialItemMap;


    }


    public static List<BurialDTO> convertBurialTraffic(List<StatisticsTileDTO> tileList) {
        // 第一层：按date分组
        // 按日期分组并按键值升序排序
        Map<String, List<StatisticsTileDTO>> dateGroup = tileList.stream()
                .collect(Collectors.groupingBy(StatisticsTileDTO::getDate))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        return dateGroup.entrySet().stream().map(dateEntry -> {
            BurialDTO dayEnvDTO = new BurialDTO();
            dayEnvDTO.setDate(dateEntry.getKey());

            long totalCount = dateEntry.getValue().stream().mapToLong(StatisticsTileDTO::getRequestCount).sum();

            // 第二层：按product分组
            Map<String, List<StatisticsTileDTO>> productGroup = dateEntry.getValue().stream()
                    .collect(Collectors.groupingBy(StatisticsTileDTO::getProduct));

            List<BurialDTO.Product> products = productGroup.entrySet().stream().map(productEntry -> {
                BurialDTO.Product product = new BurialDTO.Product();
                product.setProduct(productEntry.getKey());

                // 第三层：按burial分组合并统计值
                Map<String, List<StatisticsTileDTO>> appGroup = productEntry.getValue().stream()
                        .collect(Collectors.groupingBy(StatisticsTileDTO::getBurialItem));

                List<BurialDTO.Burial> apps = appGroup.entrySet().stream().map(appEntry -> {
                    // 合并数值字段
                    long totalRequest = appEntry.getValue().stream()
                            .mapToLong(StatisticsTileDTO::getRequestCount).sum();
                    long totalHasResult = appEntry.getValue().stream()
                            .mapToLong(StatisticsTileDTO::getHasResultCount).sum();
                    long totalNoResult = appEntry.getValue().stream()
                            .mapToLong(StatisticsTileDTO::getNoResultCount).sum();

                    BurialDTO.Burial app = new BurialDTO.Burial();
                    app.setBurialItem(appEntry.getKey());
                    app.setRequestCount(totalRequest);
                    app.setHasResultCount(totalHasResult);
                    app.setNoResultCount(totalNoResult);
                    app.setHasResultPercentage(DataCalculateUtil.calculatePercentage(totalHasResult, totalRequest));
                    return app;
                }).collect(Collectors.toList());

                // 计算region总请求数用于流量占比
                apps.forEach(app -> {
                    app.setTrafficPercentage(DataCalculateUtil.calculatePercentage(app.getRequestCount(), totalCount));
                });

                product.setDetails(apps);
                return product;
            }).collect(Collectors.toList());

            dayEnvDTO.setDetails(products);
            return dayEnvDTO;
        }).collect(Collectors.toList());
    }


    /**
     * 动态埋点分析分组
     * @param tileList
     * @return
     */
    public static List<BurialDTO> convertDyBurialTraffic(List<StatisticsTileDTO> tileList, List<GeneratedMetaDict> burialDicts) {
        // 第一层：按date分组
        // 按日期分组并按键值升序排序
        Map<String, List<StatisticsTileDTO>> dateGroup = tileList.stream()
                .collect(Collectors.groupingBy(StatisticsTileDTO::getDate))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        return dateGroup.entrySet().stream().map(dateEntry -> {
            BurialDTO dayEnvDTO = new BurialDTO();
            dayEnvDTO.setDate(dateEntry.getKey());

            long totalCount = dateEntry.getValue().stream().mapToLong(StatisticsTileDTO::getRequestCount).sum();

            // 第二层：按product分组
            Map<String, List<StatisticsTileDTO>> productGroup = dateEntry.getValue().stream()
                    .collect(Collectors.groupingBy(StatisticsTileDTO::getProduct));

            List<Product> products = productGroup.entrySet().stream().map(productEntry -> {
                Product product = new Product();
                product.setProduct(productEntry.getKey());

                List<Burial> apps = productEntry.getValue().stream()
                        .map(statisticsTileDTO -> {
                            Burial app = new Burial();
                            app.setBurialItem(statisticsTileDTO.getBurialItem());
                            app.setRequestCount(statisticsTileDTO.getRequestCount());
                            app.setHasResultCount(statisticsTileDTO.getHasResultCount());
                            app.setNoResultCount(statisticsTileDTO.getNoResultCount());
                            Map<String, Object> convertMap = new HashMap<>();
                            statisticsTileDTO.getBurialItemMap().forEach((key, value) -> {
                                String convertKey = burialDicts.stream()
                                        .filter(dict -> dict.getCode().equals(key))
                                        .map(GeneratedMetaDict::getName)
                                        .findFirst()
                                        .orElse(key);
                                convertMap.put(convertKey, value);
                            });
                            app.setBurialItemMap(convertMap);
                            app.setHasResultPercentage(DataCalculateUtil.calculatePercentage(statisticsTileDTO.getHasResultCount(), statisticsTileDTO.getRequestCount()));
                            return app;
                        }).toList();

                // 计算region总请求数用于流量占比
                apps.forEach(app -> {
                    app.setTrafficPercentage(DataCalculateUtil.calculatePercentage(app.getRequestCount(), totalCount));
                });

                product.setDetails(apps);
                return product;
            }).collect(Collectors.toList());

            dayEnvDTO.setDetails(products);
            return dayEnvDTO;
        }).collect(Collectors.toList());
    }

}
