package com.iflytek.lynxiao.support.dto.statistics;

import com.iflytek.lynxiao.support.util.excel.MyExcelCollection;
import com.iflytek.lynxiao.support.util.excel.MyExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 *
 * 来源类型（fromType）导出实体
 */
@Data
public class BurialFromTypeDTO {

    @MyExcelProperty(name = "日期")
    private String date;

    @MyExcelCollection(name = "列表")
    private List<Product> details;

    @Data
    public static class Product {

        @MyExcelProperty(name = "产品方案")
        private String product;

        @MyExcelCollection(name = "来源列表")
        private List<FromType> details;

    }

    @Data
    public static class FromType {

        @MyExcelProperty(name = "来源")
        private String fromType;

        @MyExcelProperty(name = "请求总数")
        private long requestCount;

        @MyExcelProperty(name = "有结果数")
        private long hasResultCount;

        @MyExcelProperty(name = "无结果数")
        private long noResultCount;

        @MyExcelProperty(name = "有结果占比")
        private BigDecimal hasResultPercentage;

        @MyExcelProperty(name = "流量占比")
        private BigDecimal trafficPercentage;


    }

}
