package com.iflytek.lynxiao.support.dto.statistics;

import com.iflytek.lynxiao.support.util.DataCalculateUtil;
import com.iflytek.lynxiao.support.util.excel.MyExcelCollection;
import com.iflytek.lynxiao.support.util.excel.MyExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 按环境日期段统计业务应用流量明细
 */
@Data
public class AppStatisticsDayEnvDTO {

    @MyExcelProperty(name = "日期")
    private String date;

    @MyExcelCollection(name = "环境列表")
    private List<Region> details;


    @Data
    public static class Region {
        @MyExcelProperty(name = "环境")
        private String region;


        @MyExcelCollection(name = "应用列表")
        private List<App> details;

    }

    @Data
    public static class App {
        @MyExcelProperty(name = "业务应用")
        private String app;

        @MyExcelProperty(name = "请求总数")
        private long requestCount;

        @MyExcelProperty(name = "有结果数")
        private long hasResultCount;

        @MyExcelProperty(name = "无结果数")
        private long noResultCount;

        @MyExcelProperty(name = "有结果占比")
        private BigDecimal hasResultPercentage;

        @MyExcelProperty(name = "流量占比")
        private BigDecimal trafficPercentage;


    }

    public static List<AppStatisticsDayEnvDTO> convertAppDayEnvTraffic(List<StatisticsTileDTO> tileList) {
        // 第一层：按date分组
        // 按日期分组并按键值升序排序
        Map<String, List<StatisticsTileDTO>> dateGroup = tileList.stream()
                .collect(Collectors.groupingBy(StatisticsTileDTO::getDate))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        return dateGroup.entrySet().stream().map(dateEntry -> {
            AppStatisticsDayEnvDTO dayEnvDTO = new AppStatisticsDayEnvDTO();
            dayEnvDTO.setDate(dateEntry.getKey());

            long totalCount = dateEntry.getValue().stream().mapToLong(StatisticsTileDTO::getRequestCount).sum();

            // 第二层：按region分组
            Map<String, List<StatisticsTileDTO>> regionGroup = dateEntry.getValue().stream()
                    .collect(Collectors.groupingBy(StatisticsTileDTO::getRegion));

            List<AppStatisticsDayEnvDTO.Region> regions = regionGroup.entrySet().stream().map(regionEntry -> {
                AppStatisticsDayEnvDTO.Region region = new AppStatisticsDayEnvDTO.Region();
                region.setRegion(regionEntry.getKey());

                // 第三层：按app分组合并统计值
                Map<String, List<StatisticsTileDTO>> appGroup = regionEntry.getValue().stream()
                        .collect(Collectors.groupingBy(StatisticsTileDTO::getApp));

                List<AppStatisticsDayEnvDTO.App> apps = appGroup.entrySet().stream().map(appEntry -> {
                    // 合并数值字段
                    long totalRequest = appEntry.getValue().stream()
                            .mapToLong(StatisticsTileDTO::getRequestCount).sum();
                    long totalHasResult = appEntry.getValue().stream()
                            .mapToLong(StatisticsTileDTO::getHasResultCount).sum();
                    long totalNoResult = appEntry.getValue().stream()
                            .mapToLong(StatisticsTileDTO::getNoResultCount).sum();

                    AppStatisticsDayEnvDTO.App app = new AppStatisticsDayEnvDTO.App();
                    app.setApp(appEntry.getKey());
                    app.setRequestCount(totalRequest);
                    app.setHasResultCount(totalHasResult);
                    app.setNoResultCount(totalNoResult);
                    app.setHasResultPercentage(DataCalculateUtil.calculatePercentage(totalHasResult, totalRequest));
                    return app;
                }).collect(Collectors.toList());

                // 计算region总请求数用于流量占比
                apps.forEach(app -> {
                    app.setTrafficPercentage(DataCalculateUtil.calculatePercentage(app.getRequestCount(), totalCount));
                });

                region.setDetails(apps);
                return region;
            }).collect(Collectors.toList());

            dayEnvDTO.setDetails(regions);
            return dayEnvDTO;
        }).collect(Collectors.toList());
    }

}
