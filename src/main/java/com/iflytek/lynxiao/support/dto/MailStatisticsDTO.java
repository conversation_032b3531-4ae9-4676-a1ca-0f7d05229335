package com.iflytek.lynxiao.support.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MailStatisticsDTO {

    /**
     * 日期
     */
    @Schema(title = "日期")
    private String date;

    /**
     * region
     */
    @Schema(title = "region")
    private String region;


    /**
     * 业务code
     */
    @Schema(title = "业务code")
    private String bizCode;

    /**
     * 业务id
     */
    @Schema(title = "业务id")
    private String bizId;

    /**
     * 请求总量
     */
    @Schema(title = "请求总量")
    private Long requestCount;

    /**
     * 有结果数量
     */
    @Schema(title = "有结果数量")
    private Long hasResult;

    /**
     * 无结果数量
     */
    @Schema(title = "无结果数量")
    private Long noResult;

    /**
     * 有结果占比
     */
    @Schema(title = "有结果占比")
    private BigDecimal hasResultPercentage;

    /**
     * 流量占比
     */
    @Schema(title = "流量占比")
    private BigDecimal trafficPercentage;

    /**
     * 业务模块
     */
    @Schema(title = "业务模块")
    private String module;

    /**
     * 备注
     */
    @Schema(title = "备注")
    private String remark;

}
