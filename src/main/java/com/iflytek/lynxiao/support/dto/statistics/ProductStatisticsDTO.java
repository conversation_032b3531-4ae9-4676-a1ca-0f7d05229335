package com.iflytek.lynxiao.support.dto.statistics;

import com.iflytek.lynxiao.support.util.DataCalculateUtil;
import com.iflytek.lynxiao.support.util.excel.MyExcelCollection;
import com.iflytek.lynxiao.support.util.excel.MyExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 平台流量统计- 导出产品方案维度工具类（层级结构）
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProductStatisticsDTO {

    @MyExcelProperty(name = "日期")
    private String date;



    @MyExcelCollection(name = "业务应用")
    private List<App> details;



    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class App {
        @MyExcelProperty(name = "业务应用")
        private String  app;


        @MyExcelCollection(name = "产品方案")
        private List<Product> details;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class Product {
        @MyExcelProperty(name = "产品方案")
        private String  product;


        @MyExcelCollection(name = "产品方案版本")
        private List<ProductVersion> details;

    }

    @Data
    public static class ProductVersion {
        @MyExcelProperty(name = "方案版本")
        private String  productVersion;


        @MyExcelProperty(name = "请求总数")
        private long requestCount;

        @MyExcelProperty(name = "有结果数")
        private long hasResultCount;

        @MyExcelProperty(name = "无结果数")
        private long noResultCount;

        @MyExcelProperty(name = "有结果占比")
        private BigDecimal hasResultPercentage;

        @MyExcelProperty(name = "流量占比")
        private BigDecimal trafficPercentage;

    }

    /**
     * @param tileList List<StatisticsTileDTO> tileList
     * @return List<ProductStatisticsDTO>
     */
    public static List<ProductStatisticsDTO> convertProductStatistics(List<StatisticsTileDTO> tileList) {
        // 第一层：按date分组
        Map<String, List<StatisticsTileDTO>> dateGroup = tileList.stream()
                .collect(Collectors.groupingBy(StatisticsTileDTO::getDate))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));


        return dateGroup.entrySet().stream().map(dateEntry -> {
            ProductStatisticsDTO dateDTO = new ProductStatisticsDTO();
            dateDTO.setDate(dateEntry.getKey());
            long totalCount = dateEntry.getValue().stream().mapToLong(StatisticsTileDTO::getRequestCount).sum();

            // 第二层：按app分组
            Map<String, List<StatisticsTileDTO>> appGroup = dateEntry.getValue().stream()
                    .collect(Collectors.groupingBy(StatisticsTileDTO::getApp));

            dateDTO.setDetails(appGroup.entrySet().stream().map(appEntry -> {
                ProductStatisticsDTO.App app = new ProductStatisticsDTO.App();
                app.setApp(appEntry.getKey());

                // 第三层：按product分组
                Map<String, List<StatisticsTileDTO>> productGroup = appEntry.getValue().stream()
                        .collect(Collectors.groupingBy(StatisticsTileDTO::getProduct));

                app.setDetails(productGroup.entrySet().stream().map(productEntry -> {
                    ProductStatisticsDTO.Product product = new ProductStatisticsDTO.Product();
                    product.setProduct(productEntry.getKey());

                    // 第四层：按productVersion分组合并统计值
                    Map<String, List<StatisticsTileDTO>> versionGroup = productEntry.getValue().stream()
                            .collect(Collectors.groupingBy(StatisticsTileDTO::getProductVersion));

                    List<ProductStatisticsDTO.ProductVersion> versions = versionGroup.entrySet().stream().map(versionEntry -> {
                        List<StatisticsTileDTO> items = versionEntry.getValue();
                        ProductStatisticsDTO.ProductVersion pv = new ProductStatisticsDTO.ProductVersion();
                        pv.setProductVersion(versionEntry.getKey());

                        // 合并数值字段（包含多region的聚合）
                        long totalRequest = items.stream().mapToLong(StatisticsTileDTO::getRequestCount).sum();
                        long hasResult = items.stream().mapToLong(StatisticsTileDTO::getHasResultCount).sum();
                        pv.setRequestCount(totalRequest);
                        pv.setHasResultCount(hasResult);
                        pv.setNoResultCount(totalRequest - hasResult);

                        // 计算有结果占比
                        pv.setHasResultPercentage(DataCalculateUtil.calculatePercentage(hasResult, totalRequest));
                        return pv;
                    }).collect(Collectors.toList());

                    // 计算product总请求数用于流量占比
                    versions.forEach(pv -> {
                        pv.setTrafficPercentage(DataCalculateUtil.calculatePercentage(pv.getRequestCount(), totalCount));
                    });

                    product.setDetails(versions);
                    return product;
                }).collect(Collectors.toList()));
                return app;
            }).collect(Collectors.toList()));
            return dateDTO;
        }).collect(Collectors.toList());
    }

}
