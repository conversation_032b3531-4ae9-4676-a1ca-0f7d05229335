package com.iflytek.lynxiao.support.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

import java.util.List;
import java.util.Map;

@Setter
@Getter
@Accessors(chain = true)
public class StatisticsConditionContext {

    /**
     * 应用id
     */
    private String appId;

    /**
     * 区域code
     */
    private String region;

    /**
     * 业务唯一标识id
     */
    private String bizId;

    /**
     * 产品方案id
     */
    private String productId;

    /**
     * 产品方案版本id
     */
    private String productVersionId;

    /**
     * 流程id
     */
    private String processId;

    /**
     * 埋点类型
     */
    private String burialType;

    /**
     * 埋点值
     */
    private String burialItem;

    /**
     * 动态条件列表
     */
    private List<Map.Entry<String, String>> dynamicConditions;

    /**
     * 请求总数
     */
    private Long totalNum = 0L;

    /**
     * 有结果的请求数
     */
    private Long hasResultNum = 0L;

    /**
     * 成功的请求数
     */
    private Long successNum = 0L;

    /**
     * docsCount 汇总
     */
    private Long docsCountSum = 0L;

    public static QueryBuilder buildQuerybuilder(List<Map.Entry<String, String>> dynamicConditions) {
        // 构建 QueryBuilder
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        for (Map.Entry<String, String> entry : dynamicConditions) {
            boolQuery.must(QueryBuilders.matchPhraseQuery(entry.getKey(), entry.getValue()));
        }
        return boolQuery;
    }

    public static Map<String, Object> getDynamicMap(List<Map.Entry<String, String>> dynamicConditions){
        // 将动态条件转换为Map
        return dynamicConditions.stream()
                .collect(java.util.stream.Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
