package com.iflytek.lynxiao.support.dto.statistics;

import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedMetaRegion;
import com.iflytek.lynxiao.support.repository.support.entity.AppAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.FlowAnalysis;
import com.iflytek.lynxiao.support.util.DataCalculateUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 平台流量统计工具类
 * <AUTHOR>
 */
@Data
public class StatisticalReportDTO {

    private String date;

    private long requestCount;

    private long hasResultCount;

    private long noResultCount;

    private BigDecimal trafficPercentage;

    private BigDecimal hasResultPercentage;

    private String name;

    private String code;

    private String version;

    private Long id;

    private String regionCode;

    private String regionName;

    private List<StatisticalReportDTO> son;

    private List<Map<String, Object>>  trend;

    private long value;


    // 环境级流量统计
    public static StatisticalReportDTO buildEnvReport(
            Map.Entry<String, List<AppAnalysis>> envEntry,
            Map<Long, List<FlowAnalysis>> refIdMap,
            Map<String, GeneratedMetaRegion> regionMap,
            Function<AppAnalysis, String> groupKeyFunction) {

        String regionCode = envEntry.getKey();
        GeneratedMetaRegion region = regionMap.getOrDefault(regionCode, new GeneratedMetaRegion());
        List<AppAnalysis> envAnalyses = envEntry.getValue();

        // 环境级流量统计
        List<FlowAnalysis> envFlowAnalyses = flattenFlowAnalyses(envAnalyses, refIdMap);
        Map<String, Object> envStats = calculateStats(envFlowAnalyses);

        StatisticalReportDTO envReport = new StatisticalReportDTO();
        envReport.setRegionCode(regionCode);
        envReport.setRegionName(region.getName());
        envReport.setValue((long) envStats.get("total"));
        envReport.setRequestCount((long) envStats.get("total"));
        envReport.setHasResultCount((long) envStats.get("hasResult"));
        envReport.setHasResultPercentage(DataCalculateUtil.calculatePercentage((long) envStats.get("hasResult"), (long) envStats.get("total")));

        // 日期分组统计，按照日期升序
        List<StatisticalReportDTO> dateReports = envAnalyses.stream()
                .collect(Collectors.groupingBy(groupKeyFunction))
                .entrySet().stream()
                .map(dateEntry -> buildDateReport(dateEntry, refIdMap)).sorted(Comparator.comparing(StatisticalReportDTO::getDate)).collect(Collectors.toList());

        envReport.setSon(dateReports);
        return envReport;
    }


    private static StatisticalReportDTO buildDateReport(
            Map.Entry<String, List<AppAnalysis>> dateEntry,
            Map<Long, List<FlowAnalysis>> refIdMap) {

        List<FlowAnalysis> dateFlowAnalyses = flattenFlowAnalyses(dateEntry.getValue(), refIdMap);
        Map<String, Object> dateStats = calculateStats(dateFlowAnalyses);

        StatisticalReportDTO dateReport = new StatisticalReportDTO();
        dateReport.setDate(dateEntry.getKey());
        dateReport.setRequestCount((long) dateStats.get("total"));
        dateReport.setHasResultCount((long) dateStats.get("hasResult"));
        dateReport.setNoResultCount((long) dateStats.get("noResult"));
        dateReport.setHasResultPercentage(DataCalculateUtil.calculatePercentage((long) dateStats.get("hasResult"), (long) dateStats.get("total")));
        return dateReport;
    }

    private static List<FlowAnalysis> flattenFlowAnalyses(List<AppAnalysis> appAnalyses, Map<Long, List<FlowAnalysis>> refIdMap) {
        return appAnalyses.stream()
                .map(app -> refIdMap.get(app.getId()))
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }


    private static Map<String, Object> calculateStats(List<FlowAnalysis> flowAnalyses) {
        long total = flowAnalyses.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
        long hasResult = flowAnalyses.stream().mapToLong(FlowAnalysis::getHasResult).sum();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", total);
        resultMap.put("hasResult", hasResult);
        resultMap.put("noResult", total - hasResult);
        resultMap.put("hasResultPercentage", DataCalculateUtil.calculatePercentage(hasResult, total));
        return resultMap;
    }





}
