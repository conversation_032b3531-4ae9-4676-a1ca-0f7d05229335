package com.iflytek.lynxiao.support.dto.statistics;

import com.iflytek.lynxiao.support.util.DataCalculateUtil;
import com.iflytek.lynxiao.support.util.excel.MyExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * 业务应用流量汇总
 */
@Data
public class AppStatisticsSummaryDTO {

    @MyExcelProperty(name = "业务应用")
    private String app;

    @MyExcelProperty(name = "请求总数")
    private long requestCount;

    @MyExcelProperty(name = "有结果数")
    private long hasResultCount;

    @MyExcelProperty(name = "无结果数")
    private long noResultCount;

    @MyExcelProperty(name = "有结果占比")
    private BigDecimal hasResultPercentage;

    @MyExcelProperty(name = "流量占比")
    private BigDecimal trafficPercentage;

    public static AppStatisticsSummaryDTO generateAppSummary(Map<String, Object> map) {
        AppStatisticsSummaryDTO appStatisticsSummaryDTO = new AppStatisticsSummaryDTO();
        appStatisticsSummaryDTO.setApp(map.get("name").toString());
        appStatisticsSummaryDTO.setRequestCount((long) map.get("value"));
        appStatisticsSummaryDTO.setHasResultCount((long) map.get("hasResultCount"));
        appStatisticsSummaryDTO.setNoResultCount((long) map.get("value") - (long) map.get("hasResultCount"));
        appStatisticsSummaryDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage((long) map.get("hasResultCount"), (long) map.get("value")));
        appStatisticsSummaryDTO.setTrafficPercentage((BigDecimal) map.get("percentage"));
        return appStatisticsSummaryDTO;
    }
}
