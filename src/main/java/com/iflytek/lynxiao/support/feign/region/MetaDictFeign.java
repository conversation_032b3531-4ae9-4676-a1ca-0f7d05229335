package com.iflytek.lynxiao.support.feign.region;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.boot.pandora.api.ApiResponse;


/**
 * 字典管理
 */
@Tag(name = "字典管理")
public interface MetaDictFeign {

    @Operation(summary = "通过父级code获取字典项列表")
    @GetMapping("/meta/api/v1/dict/{code}/list")
    ApiResponse getListByCode(@PathVariable("code") String code);


}
