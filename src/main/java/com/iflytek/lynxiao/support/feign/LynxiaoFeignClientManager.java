package com.iflytek.lynxiao.support.feign;

import cn.hutool.core.lang.Assert;
import com.iflytek.lynxiao.support.config.LynxiaoProperties;
import com.iflytek.lynxiao.support.exception.LynxiaoException;
import com.iflytek.lynxiao.support.feign.region.MetaRegionFeign;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.ogma.feign.OgmaFeignClientManager;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class LynxiaoFeignClientManager {

    private final LynxiaoProperties lynxiaoProperties;
    private final Map<String, String> apiGatewayCache = new HashMap<>();

    private final OgmaFeignClientManager ogmaFeignClientManager;

    public LynxiaoFeignClientManager(LynxiaoProperties lynxiaoProperties, OgmaFeignClientManager ogmaFeignClientManager) {
        Assert.notNull(lynxiaoProperties, "LynxiaoProperties must not be null");
        this.ogmaFeignClientManager = ogmaFeignClientManager;
        this.lynxiaoProperties = lynxiaoProperties;
    }

    public <T> T buildWithUrl(Class<T> feignClass, String url) {
        return this.buildWithUrl(feignClass, url, Collections.emptyMap());
    }

    /**
     * 清理feign 缓存
     */
    public void clearCache() {
        this.apiGatewayCache.clear();
    }

    /**
     * 根据URL构建Feign客户端
     *
     * @param feignClass Feign客户端类
     * @param url        http://tlb:lynxiao-portal/api/v1/xxx
     *                   注意：如果 feign 接口上的已经含有路径，只要给 http://tlb:lynxiao-portal 路径就可以了，
     *                   如：
     *                   <code>
     * @param <T>        feign 接口类型
     * @return Feign客户端代理实例
     * @PostMapping("/meta/api/v1/my/{module}") ApiResponse create(@PathVariable("module") String module, @Validated @RequestBody MetaDictDTO dto);
     * </code>
     */
    public <T> T buildWithUrl(Class<T> feignClass, String url, Map<String, String> header) {
        log.debug("Building Feign client with URL: {}", url);
        return ogmaFeignClientManager.buildWithUrl(feignClass, url, header);
    }

    public <T> T buildWithTlbServiceName(Class<T> feignClass, String serviceName) {
        return this.buildWithTlbServiceName(feignClass, serviceName, Collections.emptyMap());
    }

    public <T> T buildWithTlbServiceName(Class<T> feignClass, String serviceName, Map<String, String> header) {
        return ogmaFeignClientManager.buildWithTlbServiceName(feignClass, serviceName, header);
    }

    /**
     * 根据区域代码构建Feign客户端
     *
     * @param feignClass Feign客户端类
     * @param regionCode 区域代码
     * @param <T>        Feign客户端类型
     * @return Feign客户端实例
     */
    public <T> T buildWithRegion(Class<T> feignClass, String regionCode) {
        Assert.notNull(feignClass, "Feign class must not be null");
        log.debug("Building Feign client for region: {}", regionCode);
        String apiGateway = getOrCacheApiGateway(regionCode);
        return ogmaFeignClientManager.buildWithUrl(feignClass, apiGateway);
    }

    /**
     * 根据区域代码获取 API Gateway，并缓存结果
     *
     * @param regionCode 区域代码
     * @return API Gateway URL
     */
    private String getOrCacheApiGateway(String regionCode) {
        Assert.notBlank(regionCode, "Region code must not be blank");

        String cacheKey = "apiGateway:" + regionCode;
        if (apiGatewayCache.containsKey(cacheKey)) {
            log.info("Using cached API gateway for region: {}", regionCode);
            return apiGatewayCache.get(cacheKey);
        }

        //通过Portal 获取 Api Gateway 的网址
        MetaRegionFeign metaRegionFeign = ogmaFeignClientManager.buildWithUrl(MetaRegionFeign.class, lynxiaoProperties.getPortalServiceUrl());
        ApiResponse apiResponse = metaRegionFeign.findByCode(regionCode);
        if (apiResponse == null || apiResponse.getHeader().getCode() != 0 || apiResponse.getPayload() == null) {
            log.warn("Region code lookup failed for: {}", regionCode);
            throw new IllegalStateException("Region lookup failed for code: " + regionCode);
        }

        List<MetaRegion> regions = apiResponse.getPayload().getList("content", MetaRegion.class);
        if (regions == null || regions.isEmpty()) {
            log.warn("No region data found for code: {}", regionCode);
            throw new LynxiaoException("No region data available for code: " + regionCode);
        }

        String apiGateway = regions.get(0).getApiGateway();
        log.debug("Found region: {} with API gateway: {}", regions.get(0).getName(), apiGateway);
        this.apiGatewayCache.put(cacheKey, apiGateway);
        return apiGateway;
    }

    @Getter
    @Setter
    class MetaRegion extends Jsonable {
        private String name;
        private String code;
        private String apiGateway;
        private int envType;
    }



}