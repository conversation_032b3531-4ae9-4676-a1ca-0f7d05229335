package com.iflytek.lynxiao.support.feign.region;


import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.boot.pandora.api.ApiResponse;

/**
 * <AUTHOR>
 */
public interface MetaRegionFeign {

        @GetMapping("/api/v1/meta/region/{code}")
        ApiResponse findByCode(@PathVariable("code") String code);

        
    }