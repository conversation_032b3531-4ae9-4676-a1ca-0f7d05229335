package com.iflytek.lynxiao.support.repository.portal;

import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedOnlineProduct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface OnlineProductRepository extends JpaRepository<GeneratedOnlineProduct, Long> {

    @Query("SELECT m FROM GeneratedOnlineProduct m WHERE m.deleted = false " +
            "AND (:enabled IS NULL OR m.enabled = :enabled)" +
            "AND (:status IS NULL OR m.status = :status)")
    List<GeneratedOnlineProduct> searchList(@Param("enabled") Boolean enabled, @Param("status") Integer status);

    @Query("SELECT DISTINCT m.productId FROM GeneratedOnlineProduct m WHERE m.deleted = false AND m.enabled is TRUE ")
    List<Long> findEnabledProductIds();


}