package com.iflytek.lynxiao.support.repository.portal.entity;

import cn.hutool.core.util.IdUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

/**
 * 任务流
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "flow")
public class GeneratedFlow extends AbstractAuditingEntity<Long> {
    /**
     * 编码
     */
    @Column(name = "code")
    private String code;
    /**
     * 名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;
    /**
     * 分组
     */
    @Column(name = "group_name")
    private String groupName;
    /**
     * 流程类型，1：场景策略；2：产品方案
     */
    @Column(name = "type")
    private Integer type;
    /**
     * 区域编码
     */
    @Column(name = "region")
    private String region;
    /**
     * 是否可见，默认不可见
     */
    @Column(name = "opened")
    private Boolean opened;
    /**
     * 删除标志
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }
}