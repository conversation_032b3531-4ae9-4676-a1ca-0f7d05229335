package com.iflytek.lynxiao.support.repository.support.entity;


import cn.hutool.core.util.IdUtil;
import jakarta.persistence.PrePersist;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 公共流量分析表
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "flow_analysis")
public class FlowAnalysis extends AbstractAuditingEntity<Long> {

    /**
     * 关联业务主键(burial_analysis、app_analysisd的主键)
     */
    @Column(name = "ref_id")
    private Long refId;
    /**
     * 请求总量
     */
    @Column(name = "request_count")
    private Long requestCount;
    /**
     * 有结果数量
     */
    @Column(name = "has_result")
    private Long hasResult;
    /**
     * 无结果数量
     */
    @Column(name = "no_result")
    private Long noResult;
    /**
     * 成功数量
     */
    @Column(name = "success_count")
    private Long successCount;
    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }
}
