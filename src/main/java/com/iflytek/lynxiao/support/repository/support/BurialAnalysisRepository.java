package com.iflytek.lynxiao.support.repository.support;

import com.iflytek.lynxiao.support.repository.support.entity.BurialAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface BurialAnalysisRepository extends JpaRepository<BurialAnalysis, Long> {

    List<BurialAnalysis> findByDate(String date);

    void deleteByDateEquals(String date);

    @Query("SELECT g FROM BurialAnalysis g WHERE " +
            "g.date BETWEEN :beginDate AND :endDate " +
            "AND (COALESCE(:burialType, NULL) IS NULL OR g.burialType = :burialType)"+
            "AND (COALESCE(:regions, NULL) IS NULL OR g.region IN :regions) " +
            "AND (COALESCE(:appIds, NULL) IS NULL OR g.appId IN :appIds) " +
            "AND (COALESCE(:products, NULL) IS NULL OR g.productId IN :products)")
    List<BurialAnalysis> searchListBetweenDate(@Param("burialType") String burialType,
                                               @Param("beginDate") String beginDate,
                                               @Param("endDate") String endDate,
                                               @Param("regions") List<String> regions,
                                               @Param("products") List<Long> products,
                                               @Param("appIds") List<String> appIds);
}
