package com.iflytek.lynxiao.support.repository.portal;

import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedMetaRegion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MetaRegionRepository extends JpaRepository<GeneratedMetaRegion, Long> {

    // 查询指定id以及delete不为true的数据
    @Query("SELECT m FROM GeneratedMetaRegion m WHERE m.deleted is false AND m.id = :id")
    Optional<GeneratedMetaRegion> findById(@Param("id") Long id);

    @Query("SELECT m FROM GeneratedMetaRegion m WHERE m.deleted is false AND m.code = :code")
    Optional<GeneratedMetaRegion> findByCode(@Param("code") String code);

    // 全量查询区域
    List<GeneratedMetaRegion> findAllByDeletedFalse(Sort sort);


}