package com.iflytek.lynxiao.support.repository.portal.entity;

import cn.hutool.core.util.IdUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

/**
 * 任务流版本
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "flow_version")
public class GeneratedFlowVersion extends AbstractAuditingEntity<Long> {
    /**
     * 流程id
     */
    @Column(name = "flow_id")
    private Long flowId;
    /**
     * 版本号
     */
    @Column(name = "version")
    private Integer version;
    /**
     * 版本名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;
    /**
     * 流程拓扑id
     */
    @Column(name = "process_id")
    private String processId;
    /**
     * 1: 草稿；2：已发布
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 是否可见，默认不可见
     */
    @Column(name = "opened")
    private Boolean opened;
    /**
     * 删除标志
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }
}