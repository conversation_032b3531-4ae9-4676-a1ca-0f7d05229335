package com.iflytek.lynxiao.support.repository.portal;

import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public interface ApplicationRepository extends JpaRepository<GeneratedApplication, Long> {

    @Query("SELECT m FROM GeneratedApplication m WHERE m.deleted is false AND m.id = :id")
    Optional<GeneratedApplication> findById(@Param("id") Long id);


    @Query("SELECT m FROM GeneratedApplication m WHERE m.deleted is false")
    List<GeneratedApplication> findUnDeleted();

    @Query("SELECT m FROM GeneratedApplication m WHERE m.deleted is false AND m.id in :ids")
    List<GeneratedApplication> findByIds(@Param("ids") List<Long> ids);
}