package com.iflytek.lynxiao.support.repository.support.entity;


import cn.hutool.core.util.IdUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

/**
 * 业务唯一标识流量分析表
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "biz_equal_id_analysis")
public class BusinessEqualIdAnalysis extends AbstractAuditingEntity<Long> {

    /**
     * 日期
     */
    @Column(name = "date_str")
    private String dateStr;
    /**
     * 环境
     */
    @Column(name = "region")
    private String region;
    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;

    /**
     * 产品方案id
     */
    @Column(name = "product_id")
    private String productId;

    /**
     * 产品方案版本id
     */
    @Column(name = "product_version_id")
    private Long productVersionId;

    /**
     * 请求总量
     */
    @Column(name = "request_count")
    private Long requestCount;
    /**
     * 有结果数量
     */
    @Column(name = "has_result")
    private Long hasResult;
    /**
     * 无结果数量
     */
    @Column(name = "no_result")
    private Long noResult;

    /**
     * 返回结果数汇总
     */
    @Column(name = "docs_count_total")
    private Long docsCountTotal;

    /**
     * 分布情况
     */
    @Column(name = "distribution")
    private String distribution;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }
}
