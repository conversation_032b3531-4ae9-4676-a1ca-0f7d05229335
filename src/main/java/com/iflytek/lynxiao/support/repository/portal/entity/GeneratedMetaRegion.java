package com.iflytek.lynxiao.support.repository.portal.entity;

import cn.hutool.core.util.IdUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

/**
 * 区域
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "meta_region")
public class GeneratedMetaRegion extends AbstractAuditingEntity<Long> {
    /**
     * 名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 编码
     */
    @Column(name = "code")
    private String code;
    /**
     * api网关地址
     */
    @Column(name = "api_gateway")
    private String apiGateway;
    /**
     * 删除标志
     */
    @Column(name = "deleted")
    private Boolean deleted;
    /**
     * 环境类型
     */
    @Column(name = "env_type")
    private Integer envType;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }
}