package com.iflytek.lynxiao.support.repository.portal.entity;

import cn.hutool.core.util.IdUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

import java.time.Instant;

/**
 * 上线产品方案
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "online_product")
public class GeneratedOnlineProduct extends AbstractAuditingEntity<Long> {
    /**
     * 名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;
    /**
     * 产品方案id
     */
    @Column(name = "product_id")
    private Long productId;
    /**
     * 产品方案版本id
     */
    @Column(name = "product_version_id")
    private Long productVersionId;
    /**
     * 验证环境
     */
    @Column(name = "source_region")
    private String sourceRegion;
    /**
     * 上线环境
     */
    @Column(name = "target_region")
    private String targetRegion;
    /**
     * 流程id
     */
    @Column(name = "process_id")
    private String processId;
    /**
     * 同步时间戳(精确到毫秒)
     */
    @Column(name = "sync_ts")
    private Instant syncTs;
    /**
     * 状态，1：待同步  2：已同步
     */
    @Column(name = "status")
    private Integer status;
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    private Boolean enabled;
    /**
     * 删除标志
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }
}