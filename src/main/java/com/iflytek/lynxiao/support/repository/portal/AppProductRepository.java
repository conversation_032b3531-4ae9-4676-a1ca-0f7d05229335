package com.iflytek.lynxiao.support.repository.portal;


import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedAppProduct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AppProductRepository extends JpaRepository<GeneratedAppProduct,Long> {

    // 根据productId 查询 applicationId列表，applicationId不要重复
    @Query("SELECT DISTINCT m.applicationId FROM GeneratedAppProduct m WHERE m.deleted = false AND m.productId = :productId")
    List<Long> findApplicationIdsByProductId(@Param("productId") Long productId);

}