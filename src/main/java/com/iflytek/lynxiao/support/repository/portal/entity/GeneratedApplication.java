package com.iflytek.lynxiao.support.repository.portal.entity;

import cn.hutool.core.util.IdUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;


/**
 * 业务应用
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "application")
public class GeneratedApplication extends AbstractAuditingEntity<Long> {
    /**
     * 应用id
     */
    @Column(name = "app_id")
    private String appId;
    /**
     * 应用secret
     */
    @Column(name = "app_secret")
    private String appSecret;
    /**
     * 网关应用id
     */
    @Column(name = "gw_id")
    private String gwId;
    /**
     * 名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 类型
     */
    @Column(name = "type")
    private String type;

    /**
     * 是否启用
     */
    @Column(name = "enabled")
    private Boolean enabled;
    /**
     * 删除标志
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }
}