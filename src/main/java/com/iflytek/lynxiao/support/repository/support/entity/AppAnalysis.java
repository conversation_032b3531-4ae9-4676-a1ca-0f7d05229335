package com.iflytek.lynxiao.support.repository.support.entity;


import cn.hutool.core.util.IdUtil;
import jakarta.persistence.PrePersist;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 业务应用流量分析表
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "app_analysis")
public class AppAnalysis extends AbstractAuditingEntity<Long> {

    /**
     * 日期
     */
    @Column(name = "date_str")
    private String date;
    /**
     * 环境
     */
    @Column(name = "region")
    private String region;
    /**
     * appId
     */
    @Column(name = "app_id")
    private String appId;
    /**
     * 产品方案id
     */
    @Column(name = "product_id")
    private Long productId;
    /**
     * 产品方案版本id
     */
    @Column(name = "product_version_id")
    private Long productVersionId;
    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }

}
