package com.iflytek.lynxiao.support.repository.portal;


import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlowVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FlowVersionRepository extends JpaRepository<GeneratedFlowVersion, Long> {

    List<GeneratedFlowVersion> findByIdIn(List<Long> ids);

    @Query("SELECT m FROM GeneratedFlowVersion m WHERE m.deleted is false")
    List<GeneratedFlowVersion> findAllUndeleted();
}