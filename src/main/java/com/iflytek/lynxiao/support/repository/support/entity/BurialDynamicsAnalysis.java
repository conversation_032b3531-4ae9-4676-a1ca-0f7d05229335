package com.iflytek.lynxiao.support.repository.support.entity;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

import java.util.Map;

/**
 * 为兼容不同类型埋点需要自由组合分析流量数据而设计的mongo集合
 * <p>
 * 如  flowName+fromType   或  flowName+fromType+docsCount
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Document("burial_dynamics_analysis")
public class BurialDynamicsAnalysis extends AbstractAuditingEntity<String> {

    @Field("date_str")
    private String date;

    private String region;

    @Field("product_id")
    private String productId;

    @Field("product_version_id")
    private String productVersionId;

    @Field("app_id")
    private String appId;

    /**
     * 埋点项
     */
    @Field("burial_item")
    private Map<String, Object> burialItem;

    /**
     * 埋点组合code，对应全局字典burail_combination下的字典项code,即埋点组合的维度
     */
    @Field("burial_code")
    private String burialCode;

    @Field("request_count")
    private long requestCount;

    @Field("has_result")
    private long hasResult;

    @Field("no_result")
    private long noResult;

    @Field("success_count")
    private long successCount;


}
