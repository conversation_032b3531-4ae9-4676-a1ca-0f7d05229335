package com.iflytek.lynxiao.support.repository.portal;


import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlow;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FlowRepository extends JpaRepository<GeneratedFlow, Long> {

    @Query("SELECT m FROM GeneratedFlow m WHERE m.deleted is false AND m.id = :id")
    Optional<GeneratedFlow> findById(@Param("id") Long id);

    List<GeneratedFlow> findByIdIn(List<Long> ids);

    // 查询 给定id列表和type的记录列表
    @Query("SELECT m FROM GeneratedFlow m WHERE m.deleted is false AND m.id IN :ids AND m.type = :type")
    List<GeneratedFlow> findAllByIdsAndType(@Param("ids") List<Long> ids, @Param("type") Integer type);

}