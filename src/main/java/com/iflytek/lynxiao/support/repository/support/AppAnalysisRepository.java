package com.iflytek.lynxiao.support.repository.support;

import com.iflytek.lynxiao.support.repository.support.entity.AppAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface AppAnalysisRepository extends JpaRepository<AppAnalysis, Long> {

    List<AppAnalysis> findByDate(String date);

    void deleteByDateEquals(String date);

    @Query("SELECT g FROM AppAnalysis g WHERE " +
            "g.date BETWEEN :beginDate AND :endDate " +
            "AND (COALESCE(:appIds, NULL) IS NULL OR g.appId IN :appIds) " +
            "AND (COALESCE(:regions, NULL) IS NULL OR g.region IN :regions) " +
            "AND (COALESCE(:products, NULL) IS NULL OR g.productId IN :products)")
    List<AppAnalysis> searchListBetweenDate(@Param("beginDate") String beginDate,
                                            @Param("endDate") String endDate,
                                            @Param("appIds") List<String> appIds,
                                            @Param("regions") List<String> regions,
                                            @Param("products") List<Long> products);
}
