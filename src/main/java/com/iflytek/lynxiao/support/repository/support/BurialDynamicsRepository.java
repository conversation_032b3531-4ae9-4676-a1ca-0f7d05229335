package com.iflytek.lynxiao.support.repository.support;

import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.lynxiao.support.repository.support.entity.BurialDynamicsAnalysis;
import com.mongodb.client.result.DeleteResult;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Log4j2
@Repository
public class BurialDynamicsRepository {

    /**
     * mongo集合
     */
    private static final String COL_NAME = "burial_dynamics_analysis";


    @Resource(name = "platformMongoTemplate")
    private MongoTemplate platformMongoTemplate;


    public void deleteByDate(String date) {
        DeleteResult result = platformMongoTemplate.remove(Query.query(Criteria.where("date_str").is(date)), BurialDynamicsAnalysis.class);
        log.info("deleteByDate result: {}", result.getDeletedCount());
    }


    public void save(BurialDynamicsAnalysis entity) {
        platformMongoTemplate.save(entity, COL_NAME);
    }

    public List<BurialDynamicsAnalysis> searchListBetweenDate(String burialType,
                                                              String beginDate,
                                                              String endDate,
                                                              List<String> regions,
                                                              List<Long> products,
                                                              List<String> appIds) {

        Query query = new Query();
        // 组装各个查询条件
        query.addCriteria(Criteria.where("date_str").gte(beginDate).lte(endDate));
        if (burialType != null) {
            query.addCriteria(Criteria.where("burial_code").is(burialType));
        }
        if (CollectionUtil.isNotEmpty(regions)) {
            query.addCriteria(Criteria.where("region").in(regions));
        }
        if (CollectionUtil.isNotEmpty(products)) {
            query.addCriteria(Criteria.where("product_id").in(products));
        }
        if (CollectionUtil.isNotEmpty(appIds)) {
            query.addCriteria(Criteria.where("app_id").in(appIds));
        }
        // 执行查询
        return platformMongoTemplate.find(query, BurialDynamicsAnalysis.class, COL_NAME);
    }


}
