package com.iflytek.lynxiao.support.repository.portal.entity;

import cn.hutool.core.util.IdUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

import java.time.Instant;

/**
 * 应用关联产品
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "app_product")
public class GeneratedAppProduct extends AbstractAuditingEntity<Long> {
    /**
     * 应用id
     */
    @Column(name = "app_id")
    private String appId;
    /**
     * 产品方案id
     */
    @Column(name = "product_id")
    private Long productId;
    /**
     * 业务应用id application.id
     */
    @Column(name = "application_id")
    private Long applicationId;
    /**
     * 并发配额
     */
    @Column(name = "concurrent_quota")
    private Integer concurrentQuota;
    /**
     * QPS使用限制
     */
    @Column(name = "qps_limit")
    private Long qpsLimit;
    /**
     * QPS限制单位，1：天  2：小时，3：分钟
     */
    @Column(name = "qps_unit")
    private Integer qpsUnit;
    /**
     * 调用量限制，-1：无限量
     */
    @Column(name = "request_limit")
    private Long requestLimit;
    /**
     * 授权有效期
     */
    @Column(name = "auth_deadline")
    private Instant authDeadline;
    /**
     * 是否启用
     */
    @Column(name = "enabled")
    private Boolean enabled;
    /**
     * 协议
     */
    @Column(name = "protocol")
    private String protocol;
    /**
     * 删除标志
     */
    @Column(name = "deleted")
    private Boolean deleted;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }


}