package com.iflytek.lynxiao.support.repository.support;


import com.iflytek.lynxiao.support.repository.support.entity.FlowAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface FlowAnalysisRepository extends JpaRepository<FlowAnalysis, Long> {

    void deleteByRefIdIn(List<Long> refIds);

    List<FlowAnalysis> findByRefIdIn(List<Long> refIds);

    @Query("SELECT SUM(ar.requestCount) FROM FlowAnalysis ar")
    long getRequestCountSum();


}
