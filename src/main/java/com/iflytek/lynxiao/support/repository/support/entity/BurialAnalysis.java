package com.iflytek.lynxiao.support.repository.support.entity;


import cn.hutool.core.util.IdUtil;
import jakarta.persistence.PrePersist;
import lombok.*;
import skynet.boot.mysql.domain.AbstractAuditingEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 埋点流量分析表
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "burial_analysis")
public class BurialAnalysis extends AbstractAuditingEntity<Long> {
    /**
     * 日期
     */
    @Column(name = "date_str")
    private String date;
    /**
     * 环境
     */
    @Column(name = "region")
    private String region;
    /**
     * 产品方案id
     */
    @Column(name = "product_id")
    private Long productId;
    /**
     * 产品方案版本id
     */
    @Column(name = "product_version_id")
    private Long productVersionId;
    /**
     * 埋点类型
     */
    @Column(name = "burial_type")
    private String burialType;
    /**
     * 埋点项
     */
    @Column(name = "burial_item")
    private String burialItem;

    /**
     * 业务应用
     */
    @Column(name = "app_id")
    private String appId;

    @PrePersist
    public void prePersist() {
        if (this.getId() == null) {
            this.setId(IdUtil.getSnowflakeNextId());
        }
    }
}
