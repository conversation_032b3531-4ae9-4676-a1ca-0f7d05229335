package com.iflytek.lynxiao.support.exception;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import skynet.boot.exception.SkynetErrorCode;
import skynet.boot.pandora.api.ApiResponseGenerics;
import skynet.boot.pandora.api.ApiResponseHeader;
import skynet.boot.pandora.brave.TraceUtils;

/**
 *
 **/
@Slf4j
@RestControllerAdvice
public class GlobalExceptionAdvice {

    @Resource
    private TraceUtils traceUtils;


    @ExceptionHandler(NoResourceFoundException.class)
    public ApiResponseGenerics<?> handleNoResourceFoundException(Exception e) {
        log.warn("NoResourceFoundException :{}", e.getMessage());
        ApiResponseGenerics<?> ret = new ApiResponseGenerics<>();
        ret.setHeader(new ApiResponseHeader("", SkynetErrorCode.ERROR.getCode(), e.getMessage()));
        return ret;
    }
}