package com.iflytek.lynxiao.support.exception;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.exception.SkynetErrorCode;
import skynet.boot.exception.SkynetException;

/**
 * <AUTHOR>  2024/10/22 16:20
 */
@Getter
public class LynxiaoException extends SkynetException {

    private static final long serialVersionUID = 501284138601L;

    @Setter
    private String traceId;

    public LynxiaoException() {
        super(SkynetErrorCode.ERROR);
    }

    public LynxiaoException(String message) {
        super(SkynetErrorCode.ERROR.getCode(), message);
    }


    public LynxiaoException(String message, Throwable cause) {
        super(SkynetErrorCode.ERROR.getCode(), message, cause);
    }

    public LynxiaoException(int code, String message) {
        super(code, message);
    }


}
