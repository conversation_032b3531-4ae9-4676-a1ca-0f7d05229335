package com.iflytek.lynxiao.support.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import skynet.boot.exception.SkynetErrorCode;
import skynet.boot.exception.SkynetException;
import skynet.boot.exception.message.ErrorMessage;
import skynet.boot.exception.message.ExceptionMessageFormatter;
import skynet.boot.exception.message.SkynetErrorMessage;

/**
 * <AUTHOR>  2024/9/29 17:08
 */
@Slf4j
@Component
public class LynxiaoExceptionMessageFormatter implements ExceptionMessageFormatter {

    /**
     * 转换通用异常
     */
    @Override
    public ErrorMessage format(Throwable e, int code) {
        log.error("系统异常", e);
        if (e instanceof MethodArgumentNotValidException) {
            return new SkynetErrorMessage(getCurrentTraceId(), SkynetErrorCode.PARAM_INVALID.getCode(), buildMethodArgumentNotValidExceptionMessage((MethodArgumentNotValidException) e));
        } else if (e instanceof LynxiaoException) {
            return new SkynetErrorMessage(getCurrentTraceId(), code, e.getMessage());
        } else {
            return new SkynetErrorMessage(getCurrentTraceId(), code, "系统异常，请联系管理员！");
        }
    }

    private String buildMethodArgumentNotValidExceptionMessage(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder sb = new StringBuilder("参数异常：");
        for (FieldError fieldError : bindingResult.getFieldErrors()) {
            sb.append("【")
                    .append(fieldError.getField())
                    .append("】")
                    .append(fieldError.getDefaultMessage())
                    .append("；");
        }
        return sb.toString();
    }

    /**
     * 转换 Skynet 异常
     */
    @Override
    public ErrorMessage format(SkynetException e) {
        log.error("系统异常", e);
        return new SkynetErrorMessage(getCurrentTraceId(), e.getCode(), "系统异常，请联系管理员！");
    }

}
