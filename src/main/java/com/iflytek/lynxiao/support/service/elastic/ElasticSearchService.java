package com.iflytek.lynxiao.support.service.elastic;

import com.alibaba.fastjson2.JSONObject;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.QueryBuilder;

import java.util.List;
import java.util.Map;

public interface ElasticSearchService {
    // 组装查询条件
    List<QueryBuilder> recursionCondition(String jsonStr);

    // 查询总数
    long countDocuments(String region, String indexName, List<QueryBuilder> queries);

    long queryTotalNum(String region, String indexName, String module, String bizCode);

    List<JSONObject> getDocuments(String region, String indexName, List<QueryBuilder> queries, Integer limit);


    /**
     * 获取某一字段所有枚举值列表
     * @param region     区域
     * @param indexName  索引
     * @param field      字段
     * @return            枚举值列表
     */
    List<String> aggregationQuery(String region, String indexName, String field);


    /**
     * 获取某一字段所有枚举值列表
     * @param region     区域
     * @param indexName  索引
     * @param field      字段
     * @param filters    额外过滤字段
     * @return            枚举值列表
     */
    List<String> aggregationQuery(String region, String indexName, String field, Map<String,String> filters);

    /**
     * 获取某一个业务唯一标识的请求总数
     * @param region     区域
     * @param indexName  索引
     * @param field      字段
     * @param filters    额外过滤字段
     * @return            枚举值列表
     */
    Long aggregationGetRequestCount(String region, String indexName, String field, Map<String,String> filters);


    /**
     * 获取满足条件的ES日志（分批查询,如果maxTotalDocs为null, 则查询该indexName下所有数据）
     * @param region  区域
     * @param indexName 索引
     * @param queries  条件
     * @param keepAlive   Scroll上下文的存活时间
     * @param pageSize   每页返回的数量
     * @param maxTotalDocs   最大记录
     * @return  List<JSONObject>
     */
    List<JSONObject> getAllDocumentsByScrollWithMax(String region, String indexName, List<QueryBuilder> queries, TimeValue keepAlive, Integer pageSize, Integer maxTotalDocs);
}
