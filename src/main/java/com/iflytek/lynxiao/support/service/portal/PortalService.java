package com.iflytek.lynxiao.support.service.portal;

import com.iflytek.lynxiao.support.dto.FlowDTO;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedApplication;

import java.util.List;

/**
 * <AUTHOR>
 * 中心数据服务
 */
public interface PortalService {

    /**
     * 获取已经启用的业务应用列表
     * @return  业务应用
     */
    List<GeneratedApplication> getEnableAppList();

    /**
     * 获取已经上线的产品方案列表
     * @return  产品方案
     */
    List<FlowDTO> getOnlineProductList();
}
