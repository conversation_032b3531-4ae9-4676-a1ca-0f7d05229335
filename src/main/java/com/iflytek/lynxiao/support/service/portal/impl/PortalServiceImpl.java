package com.iflytek.lynxiao.support.service.portal.impl;

import cn.hutool.core.bean.BeanUtil;
import com.iflytek.lynxiao.support.consts.FlowType;
import com.iflytek.lynxiao.support.dto.FlowDTO;
import com.iflytek.lynxiao.support.repository.portal.ApplicationRepository;
import com.iflytek.lynxiao.support.repository.portal.FlowRepository;
import com.iflytek.lynxiao.support.repository.portal.OnlineProductRepository;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedApplication;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlow;
import com.iflytek.lynxiao.support.service.portal.PortalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PortalServiceImpl implements PortalService {

    @Autowired
    private ApplicationRepository applicationRepository;

    @Resource
    private OnlineProductRepository onlineProductRepository;

    @Resource
    private FlowRepository flowRepository;


    @Override
    public List<GeneratedApplication> getEnableAppList() {
        return applicationRepository.findUnDeleted();
    }

    @Override
    public List<FlowDTO> getOnlineProductList() {
        List<Long> productIds = this.onlineProductRepository.findEnabledProductIds();
        List<GeneratedFlow> products = this.flowRepository.findAllByIdsAndType(productIds, FlowType.product.getCode());
        return products.stream().map(flow -> BeanUtil.copyProperties(flow, FlowDTO.class)).collect(Collectors.toList());
    }
}
