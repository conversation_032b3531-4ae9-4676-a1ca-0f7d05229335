package com.iflytek.lynxiao.support.service.mail;

import org.jfree.data.category.DefaultCategoryDataset;

import java.util.Map;

public interface MailService {

    /**
     * 发送 html 的邮件
     *
     * @param to      （多个人用英文逗号拼接）
     * @param cc      （多个人用英文逗号拼接）
     * @param subject 主题
     * @param html    内容
     */
    void sendWithHtml(String to, String cc, String subject, String html);

    /**
     * 发送 html 的邮件附带折线图
     *
     * @param to      （多个人用英文逗号拼接）
     * @param cc      （多个人用英文逗号拼接）
     * @param subject 主题
     * @param html    内容
     */
    void sendEmailWithMultipleChartsAndTable(String to, String cc, String subject, String htmlTable, Map<String, DefaultCategoryDataset> chartDatasets);
}
