package com.iflytek.lynxiao.support.service.mail.impl;

import com.iflytek.lynxiao.support.service.mail.MailService;
import com.iflytek.lynxiao.support.util.ChartUtils;
import jakarta.annotation.Resource;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.angus.mail.util.MailSSLSocketFactory;
import org.jfree.data.category.DefaultCategoryDataset;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Map;
import java.util.Properties;


@Log4j2
@Service
public class MailServiceImpl implements MailService {


    @Resource
    private JavaMailSender javaMailSender;

    @Resource
    private MailProperties mailProperties;

    @Override
    public void sendWithHtml(String to, String cc, String subject, String html) {
        log.info("## Ready to send mail ...");
        if (StringUtils.isEmpty(to)) {
            log.info("未配置收件人!");
        }

        addSslConfig(javaMailSender);
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();

        MimeMessageHelper mimeMessageHelper = null;
        try {
            mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
            // 邮件发送来源
            mimeMessageHelper.setFrom(mailProperties.getUsername());
            // 邮件发送目标
            mimeMessageHelper.setTo(to.split(","));
            // 抄送人
            if (StringUtils.isNotBlank(cc)) {
                mimeMessageHelper.setCc(cc.split(","));
            }
            // 设置标题
            mimeMessageHelper.setSubject(subject);
            // 设置内容，并设置内容 html 格式为 true
            mimeMessageHelper.setText(html, true);

            javaMailSender.send(mimeMessage);
            log.info("## Send the mail with html success ...");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("Send html mail error: ", e);
        }

    }

    @Override
    public void sendEmailWithMultipleChartsAndTable(String to, String cc, String subject, String htmlTable, Map<String, DefaultCategoryDataset> chartDatasets) {
        // 创建邮件消息

        try {
            addSslConfig(javaMailSender);
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);

            mimeMessageHelper.setFrom(mailProperties.getUsername());
            mimeMessageHelper.setTo(to.split(","));
            if (StringUtils.isNotBlank(cc)) {
                mimeMessageHelper.setCc(cc.split(","));
            }
            mimeMessageHelper.setSubject(subject);

            // 组合HTML内容
            StringBuilder htmlContent = new StringBuilder();
            htmlContent.append(getHtmlContent());

            // 生成并添加折线图
            int chartIndex = 0;
            for (Map.Entry<String, DefaultCategoryDataset> entry : chartDatasets.entrySet()) {
                String chartTitle = entry.getKey();
                DefaultCategoryDataset dataset = entry.getValue();

                // 生成折线图并保存为文件
                File chartFile = ChartUtils.generateLineChart(chartTitle, dataset);

                // 检查文件是否存在
                if (!chartFile.exists()) {
                    throw new FileNotFoundException("图表文件未生成: " + chartFile.getAbsolutePath());
                }

                // 添加图片引用到HTML内容
                htmlContent.append("<h3>").append(chartTitle).append("</h3>");
                htmlContent.append("<img src='cid:chart").append(chartIndex).append("'>");

                // 添加折线图作为内嵌资源
                FileSystemResource fileSystemResource = new FileSystemResource(chartFile);
                mimeMessageHelper.addInline("chart" + chartIndex, fileSystemResource);

                chartIndex++;
            }

            // 添加HTML表格
            htmlContent.append(htmlTable);


            htmlContent.append("</body></html>");

            // 设置邮件内容
            mimeMessageHelper.setText(htmlContent.toString(), true);

            // 发送邮件
            javaMailSender.send(mimeMessage);

            // 删除临时生成的图表文件
            for (Map.Entry<String, DefaultCategoryDataset> entry : chartDatasets.entrySet()) {
                String chartTitle = entry.getKey();
                File chartFile = new File("charts", chartTitle + ".png");
                if (chartFile.exists()) {
                    chartFile.delete();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


        log.info("## Send the mail with multiple charts and table success ...");
    }


    /**
     * SSL配置
     *
     * @param mailSender
     */
    private void addSslConfig(JavaMailSender mailSender) {
        if (!(mailSender instanceof JavaMailSenderImpl)) {
            log.warn("自定义邮件实现，如果使用SSL，请进行自定义配置！");
            return;
        }
        JavaMailSenderImpl impl = (JavaMailSenderImpl) mailSender;
        if ("true".equals(impl.getJavaMailProperties().getProperty("mail.smtp.ssl.enable"))) {
            //已经配置过就不再配置，避免关闭session造成问题
            return;
        }
        Properties props = new Properties();
        props.put("mail.smtp.sendpartial", true);
        props.put("mail.smtp.auth", "true");
        MailSSLSocketFactory sf = null;
        try {
            sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // do nothing
        }
        props.put("mail.smtp.ssl.socketFactory", sf);
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        impl.setJavaMailProperties(props);
    }

    private String getHtmlContent() {

        return "<!DOCTYPE html> <html lang=\"en\"> <head> <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"> <title>自研SS每日流量数据</title>" +
//                    "                        <body>" +
                "                        <style>" +
                "                             body {" +
                "                                   font-family: Arial, sans-serif;" +
                "                                   font-size: 14px; " +
                "                                   color: #333;" +
                "                                   }" +
                "                            table {" +
                "                                border-collapse: collapse; /* 合并边框 */" +
//                    "                                width: 100%;" +
                "                                background-color: #fff; /* 设置表格背景颜色为白色 */" +
                "                                color: #333; /* 设置字体颜色为黑色 */" +
                "                                font-size: 14px; /* 设置字号为14px */" +
                "                                border: 1px solid #ccc; /* 设置1像素宽度的灰色边框 */" +
                "                            }" +
                "                            th, td {" +
                "                                padding: 10px; /* 设置单元格内边距为8像素 */" +
                "                                border: 1px solid #ccc; /* 单元格边框 */" +
                "                            }" +
                "                            td {" +
                "                                 text-align: right; " +
                "                            }" +
                "                            th {" +
                "                                background-color: #4F94CD; /* 设置表头背景颜色 */" +
                "                                font-weight: bold; /* 设置表头字体为加粗 */" +
                "                                color: #FFFFFF;" +
                "                            }" +
                "                            caption {" +
//                    "                                color: #000;" +
                "                                /*font: italic 85%/1 arial,sans-serif;*/" +
                "                                font-size: 18px; /* 设置字号为14px */" +
                "                                padding: 1em 0;" +
                "                                text-align: left;" +
                "                            }" +
                "                            p {" +
                "                                color: #000;" +
                "                                /*font: italic 85%/1 arial,sans-serif;*/" +
                "                               font-size: 18px; /* 设置字号为14px */" +
                "                                padding: 1px 0;" +
                "                                text-align: left;" +
                "                            }" +
                "                            span {" +
                "                                font-weight: bold;" +
                "                                font-size: 20px; /* 设置字号为14px */" +
                "                            }" +
                "                        </style>" +
                "                       </head>" +
                "                       <body>" +
                "                        <p>" +
                "                           各位好！凌霄平台(Lynxiao) 请求访问统计报表，如下：" +
                "                        </p>"+
                "                        <br><br>";

    }

}
