package com.iflytek.lynxiao.support.service.statistics.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.support.consts.BurialStatisticsModel;
import com.iflytek.lynxiao.support.consts.RegionCode;
import com.iflytek.lynxiao.support.dto.common.GeneratedMetaDict;
import com.iflytek.lynxiao.support.dto.statistics.*;
import com.iflytek.lynxiao.support.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.support.feign.region.MetaDictFeign;
import com.iflytek.lynxiao.support.repository.portal.ApplicationRepository;
import com.iflytek.lynxiao.support.repository.portal.FlowRepository;
import com.iflytek.lynxiao.support.repository.portal.FlowVersionRepository;
import com.iflytek.lynxiao.support.repository.portal.MetaRegionRepository;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedApplication;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlow;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlowVersion;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedMetaRegion;
import com.iflytek.lynxiao.support.repository.support.AppAnalysisRepository;
import com.iflytek.lynxiao.support.repository.support.BurialAnalysisRepository;
import com.iflytek.lynxiao.support.repository.support.BurialDynamicsRepository;
import com.iflytek.lynxiao.support.repository.support.FlowAnalysisRepository;
import com.iflytek.lynxiao.support.repository.support.entity.AppAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.BurialAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.BurialDynamicsAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.FlowAnalysis;
import com.iflytek.lynxiao.support.service.statistics.PlatformStatisticsService;
import com.iflytek.lynxiao.support.util.DataCalculateUtil;
import com.iflytek.lynxiao.support.util.DateUtil;
import com.iflytek.lynxiao.support.util.StatisticsUtil;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.ApiResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Transactional
@Log4j2
@Service
public class PlatformStatisticsServiceImpl implements PlatformStatisticsService {

    @Autowired
    private FlowVersionRepository flowVersionRepository;

    @Autowired
    private FlowRepository flowRepository;

    @Autowired
    private AppAnalysisRepository appAnalysisRepository;

    @Autowired
    private BurialAnalysisRepository burialAnalysisRepository;

    @Autowired
    private FlowAnalysisRepository flowAnalysisRepository;

    @Resource
    private MetaRegionRepository metaRegionRepository;

    @Resource
    private ApplicationRepository applicationRepository;

    @Resource
    private LynxiaoFeignClientManager lynxiaoFeignClientManager;

    @Resource
    private BurialDynamicsRepository burialDynamicsRepository;

    @Resource
    private StatisticsUtil statisticsUtil;

    @Override
    public JSONObject getAppTraffic(String beginDate, String endDate, String appIds, String regions) {

        JSONObject result = new JSONObject();
        // 数据获取
        List<AppAnalysis> analysisList = searchAppAnalysisListBetweenDate(beginDate, endDate, appIds, regions, null);
        List<FlowAnalysis> flowAnalysisList = getFlowAnalysis(analysisList);
        Map<Long, List<FlowAnalysis>> refIdMap = flowAnalysisList.stream()
                .collect(Collectors.groupingBy(FlowAnalysis::getRefId));

        // 基础统计数据: 请求总量、有结果数量、成功数量
        long totalRequestCount = flowAnalysisList.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
        long hasResultRequestCount = flowAnalysisList.stream().mapToLong(FlowAnalysis::getHasResult).sum();
        long successRequestCount = flowAnalysisList.stream().mapToLong(FlowAnalysis::getSuccessCount).sum();
        long totalDay = DateUtil.getBetweenDateCount(beginDate, endDate); // 间隔天数

        // 1. 总请求量统计
        result.put("totalRequest", buildStatisticMap(totalRequestCount, totalDay));
        // 2. 成功率统计
        result.put("successRequest", buildRateStatistics(successRequestCount, totalRequestCount, "成功数量", "失败数量"));
        // 3. 有结果率统计
        result.put("hasResult", buildHasResultStatistics(hasResultRequestCount, totalRequestCount, beginDate, endDate, appIds, regions));
        // 4. 应用流量占比
        result.put("app", buildAppTrafficStats(analysisList, refIdMap, totalRequestCount));
        // 5. 检索请求趋势分析-总览
        result.put("overview", buildTrendOverview(totalRequestCount, beginDate, endDate, appIds, regions));
        // 6. 检索请求趋势分析-趋势图
        result.put("trend", buildTrendAnalysis(analysisList, refIdMap, beginDate, endDate));
        // 7. 产品方案分析
        result.put("productTrend", buildProductAnalysis(analysisList, refIdMap, totalRequestCount, beginDate, endDate));

        return result;
    }

    @Override
    public List<Object> getBurialTraffic(String beginDate, String endDate, String appIds, String regions, String productIds) {

        // 1. 数据获取与预处理
        List<BurialAnalysis> burialAnalyses = searchBurialAnalysisListBetweenDate(null, beginDate, endDate, regions, productIds, appIds);
        List<FlowAnalysis> flowAnalysisList = getBurialAnalysis(burialAnalyses);
        Map<Long, FlowAnalysis> refIdMap = flowAnalysisList.stream()
                .collect(Collectors.toMap(FlowAnalysis::getRefId, Function.identity()));

        // 获取产品方案映射，用于回显数据
        Map<Long, GeneratedFlow> flowMap = getProductFlowMap(burialAnalyses);

        // 2. 按埋点类型分组统计
        Map<String, List<BurialAnalysis>> burialTypeMap = burialAnalyses.stream()
                .collect(Collectors.groupingBy(BurialAnalysis::getBurialType));

        // 获取埋点字典配置
        Map<String, String> filedModelMap = new HashMap<>();
        Map<String, GeneratedMetaDict> nameDictMap = new HashMap<>();
        initBurialDictConfig(filedModelMap, nameDictMap);

        // 3. 按模板处理数据
        List<Object> resultList = new ArrayList<>();
        handleBurialDataByModel(burialTypeMap, filedModelMap, nameDictMap, flowMap, refIdMap, beginDate, endDate, resultList, regions, productIds, appIds);

        return resultList;
    }

    @Override
    public JSONObject getEnvTraffic(String beginDate, String endDate, String appIds, String regionStr, String productIds) {
        JSONObject result = new JSONObject();
        // 数据获取
        List<AppAnalysis> analysisList = searchAppAnalysisListBetweenDate(beginDate, endDate, appIds, regionStr, productIds);
        List<FlowAnalysis> flowAnalysisList = getFlowAnalysis(analysisList);
        Map<Long, List<FlowAnalysis>> refIdMap = flowAnalysisList.stream().collect(Collectors.groupingBy(FlowAnalysis::getRefId));
        List<GeneratedMetaRegion> regions = metaRegionRepository.findAllByDeletedFalse(Sort.by("code"));
        Map<String, GeneratedMetaRegion> regionMap = regions.stream().collect(Collectors.toMap(GeneratedMetaRegion::getCode, t -> t));

        // 判断是否跨年，跨年按月统计，否则按天，app.getDate().substring(0, 7) 得到的是 yyyy-MM
        boolean isGroupByMonth = DateUtil.isExactlyOneYearApart(beginDate, endDate);
        Function<AppAnalysis, String> groupKeyFunction = app ->
                isGroupByMonth ? app.getDate().substring(0, 7) : app.getDate();

        // 按环境分组处理
        List<StatisticalReportDTO> reportList = analysisList.stream()
                .collect(Collectors.groupingBy(AppAnalysis::getRegion))
                .entrySet().stream()
                .map(envEntry -> StatisticalReportDTO.buildEnvReport(envEntry, refIdMap, regionMap, groupKeyFunction))
                .collect(Collectors.toList());

        result.put("result", reportList);
        return result;
    }

    @Override
    public List<StatisticsTileDTO> getTileStatisticsTraffic(String beginDate, String endDate, String appIds, String regions) {

        // 1. 数据获取
        List<AppAnalysis> analysisList = searchAppAnalysisListBetweenDate(beginDate, endDate, appIds, regions, null);
        if (CollectionUtils.isEmpty(analysisList)) {
            return new ArrayList<>();
        }
        List<FlowAnalysis> flowAnalysisList = getFlowAnalysis(analysisList);
        long totalRequestCount = flowAnalysisList.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
        Map<Long, FlowAnalysis> refIdMap = flowAnalysisList.stream().collect(Collectors.toMap(FlowAnalysis::getRefId, t -> t));
        // 获取应用列表，方便后续组装名称
        Map<String, GeneratedApplication> appInfoMap = applicationRepository.findUnDeleted().stream()
                .collect(Collectors.toMap(GeneratedApplication::getAppId, t -> t));
        // 获取产品方案列表，方便后续组装名称、编码
        List<Long> productIds = analysisList.stream().map(AppAnalysis::getProductId).collect(Collectors.toList());
        Map<Long, GeneratedFlow> productInfoMap = flowRepository.findByIdIn(productIds).stream()
                .collect(Collectors.toMap(GeneratedFlow::getId, t -> t));
        // 获取产品方案版本列表，方便后续组装名称、版本号
        List<Long> productVersionIds = analysisList.stream().map(AppAnalysis::getProductVersionId).collect(Collectors.toList());
        Map<Long, GeneratedFlowVersion> productVerionInfoMap = flowVersionRepository.findByIdIn(productVersionIds).stream()
                .collect(Collectors.toMap(GeneratedFlowVersion::getId, t -> t));
        // 获取区域列表，后续组装数据
        Map<String, GeneratedMetaRegion> regionMap = metaRegionRepository.findAllByDeletedFalse(Sort.by("code")).stream().collect(Collectors.toMap(GeneratedMetaRegion::getCode, t -> t));


        // 2. 组装平铺的数据
        List<StatisticsTileDTO> exportList = new ArrayList<>();
        for (AppAnalysis appAnalysis : analysisList) {
            exportList.add(getProductExportDTO(appAnalysis, refIdMap, appInfoMap, productInfoMap, productVerionInfoMap, regionMap, totalRequestCount));
        }
        return exportList;
    }

    @Override
    public List<AppStatisticsSummaryDTO> exportAppSummaryTraffic(String beginDate, String endDate, String appIds, String regions) {
        // 1. 数据获取
        List<AppAnalysis> analysisList = searchAppAnalysisListBetweenDate(beginDate, endDate, appIds, regions, null);
        if (CollectionUtils.isEmpty(analysisList)) {
            return new ArrayList<>();
        }
        List<FlowAnalysis> flowAnalysisList = getFlowAnalysis(analysisList);
        long totalRequestCount = flowAnalysisList.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
        Map<Long, List<FlowAnalysis>> refIdMap = flowAnalysisList.stream().collect(Collectors.groupingBy(FlowAnalysis::getRefId));

        // 2. 流量统计
        List<Map<String, Object>> trafficStatsMap = buildAppTrafficStats(analysisList, refIdMap, totalRequestCount);
        if (CollectionUtil.isEmpty(trafficStatsMap)) {
            return new ArrayList<>();
        }
        // 3. 组装返回数据
        List<AppStatisticsSummaryDTO> summaryDTOList = new ArrayList<>();
        for (Map<String, Object> map : trafficStatsMap) {
            summaryDTOList.add(AppStatisticsSummaryDTO.generateAppSummary(map));
        }
        return summaryDTOList;
    }

    @Override
    public List<AppStatisticsDayDTO> exportAppDayTraffic(String beginDate, String endDate, String appIds, String regions) {
        // 1. 数据获取
        List<AppAnalysis> analysisList = searchAppAnalysisListBetweenDate(beginDate, endDate, appIds, regions, null);
        if (CollectionUtils.isEmpty(analysisList)) {
            return Collections.emptyList();
        }
        // 获取流量数据
        List<FlowAnalysis> flowAnalysisList = getFlowAnalysis(analysisList);

        // 预计算每个refId对应的总请求和结果数
        Map<Long, long[]> refIdSumMap = new HashMap<>();
        flowAnalysisList.forEach(flow -> {
            long[] sums = refIdSumMap.computeIfAbsent(flow.getRefId(), k -> new long[2]);
            sums[0] += flow.getRequestCount();
            sums[1] += flow.getHasResult();
        });

        // 获取应用信息,方便后续组装应用名称信息
        Map<String, GeneratedApplication> appInfoMap = applicationRepository.findUnDeleted()
                .stream()
                .collect(Collectors.toMap(
                        GeneratedApplication::getAppId,
                        Function.identity(),
                        (existing, replacement) -> existing)
                );

        // 2.流量统计：按日期统计 超过一年统计维度 yyyy-MM
        boolean isGroupByMonth = DateUtil.isExactlyOneYearApart(beginDate, endDate);
        Function<AppAnalysis, String> groupKeyFunction = app ->
                isGroupByMonth ? app.getDate().substring(0, 7) : app.getDate();

        return analysisList.stream()
                .collect(Collectors.groupingBy(groupKeyFunction))
                .entrySet()
                .stream()
                .map(entry -> createDayDTO(entry.getKey(), entry.getValue(), refIdSumMap, appInfoMap))
                .sorted(Comparator.comparing(AppStatisticsDayDTO::getDate).reversed()) // 按日期排序
                .collect(Collectors.toList());
    }

    @Override
    public List<AppStatisticsDayEnvDTO> exportAppDayEnvTraffic(String beginDate, String endDate, String appIds, String regions) {
        // 1. 获取平铺结构数据
        List<StatisticsTileDTO> tileStatisticsTraffic = getTileStatisticsTraffic(beginDate, endDate, appIds, regions);
        if (CollectionUtils.isEmpty(tileStatisticsTraffic)) {
            return new ArrayList<>();
        }
        tileStatisticsTraffic.sort(Comparator.comparing(StatisticsTileDTO::getDate).reversed());
        // 2. 转换为导出层级结构(date->region->app)
        return AppStatisticsDayEnvDTO.convertAppDayEnvTraffic(tileStatisticsTraffic);
    }

    @Override
    public List<ProductEnvStatisticsDTO> exportProductDayEnvTraffic(String beginDate, String endDate, String appIds, String regions) {
        // 1. 获取平铺结构数据
        List<StatisticsTileDTO> tileStatisticsTraffic = getTileStatisticsTraffic(beginDate, endDate, appIds, regions);
        if (CollectionUtils.isEmpty(tileStatisticsTraffic)) {
            return new ArrayList<>();
        }
        tileStatisticsTraffic.sort(Comparator.comparing(StatisticsTileDTO::getDate).reversed());
        // 2. 转换为导出层级结构(date->app->product->productVersion->region)
        return ProductEnvStatisticsDTO.convertProductEnv(tileStatisticsTraffic);
    }

    @Override
    public List<ProductStatisticsDTO> exportProductDayTraffic(String beginDate, String endDate, String appIds, String regions) {
        // 1. 获取平铺结构数据
        List<StatisticsTileDTO> tileStatisticsTraffic = getTileStatisticsTraffic(beginDate, endDate, appIds, regions);
        if (CollectionUtils.isEmpty(tileStatisticsTraffic)) {
            return new ArrayList<>();
        }
        tileStatisticsTraffic.sort(Comparator.comparing(StatisticsTileDTO::getDate).reversed());
        // 2. 转换为导出层级结构(date->app->product->productVersion)
        return ProductStatisticsDTO.convertProductStatistics(tileStatisticsTraffic);
    }

    @Override
    public List<BurialDTO> exportBurialTraffic(String burialType, String beginDate, String endDate, String appIds, String regions) {
        // 1. 获取平铺结构数据
        List<StatisticsTileDTO> tileStatisticsTraffic = getBurialTileStatisticsTraffic(burialType, beginDate, endDate, appIds, regions);
        if (CollectionUtils.isEmpty(tileStatisticsTraffic)) {
            return new ArrayList<>();
        }
        tileStatisticsTraffic.sort(Comparator.comparing(StatisticsTileDTO::getDate).reversed());
        // 2. 转换为导出层级结构(date->product->burialItem)
        return BurialDTO.convertBurialTraffic(tileStatisticsTraffic);
    }

    @Override
    public List<BurialDTO> exportDynamicsBurialTraffic(String burialType, String beginDate, String endDate, String appIds, String regions) {
        // 1. 获取平铺结构数据
        List<StatisticsTileDTO> tileStatisticsTraffic = getDynamicsBurialTileStatisticsTraffic(burialType, beginDate, endDate, appIds, regions);
        if (CollectionUtils.isEmpty(tileStatisticsTraffic)) {
            return new ArrayList<>();
        }
        tileStatisticsTraffic.sort(Comparator.comparing(StatisticsTileDTO::getDate).reversed());
        MetaDictFeign metaDictFeign = lynxiaoFeignClientManager.buildWithRegion(MetaDictFeign.class, RegionCode.HF);
        List<GeneratedMetaDict> burialDicts = new ArrayList<>();
        Arrays.asList(BurialStatisticsModel.ONE.getCode(), BurialStatisticsModel.TWO.getCode(), BurialStatisticsModel.THREE.getCode()).forEach(t -> {
            ApiResponse apiResponse = metaDictFeign.getListByCode(t);
            List<GeneratedMetaDict> itemList = apiResponse.getPayload().getList("data", GeneratedMetaDict.class);
            burialDicts.addAll(itemList);
        });
        // 2. 转换为导出层级结构(date->product->burialItem)
        return BurialDTO.convertDyBurialTraffic(tileStatisticsTraffic, burialDicts);
    }

    @Override
    public List<StatisticsTileDTO> exportTileDynamicsBurialTraffic(String burialType, String beginDate, String endDate, String appIds, String regions) {
        // 1. 获取平铺结构数据
        List<StatisticsTileDTO> tileStatisticsTraffic = getDynamicsBurialTileStatisticsTraffic(burialType, beginDate, endDate, appIds, regions);
        if (CollectionUtils.isEmpty(tileStatisticsTraffic)) {
            return new ArrayList<>();
        }
        tileStatisticsTraffic.sort(Comparator.comparing(StatisticsTileDTO::getDate).reversed());
        return tileStatisticsTraffic;
    }

    @Override
    public List<GeneratedMetaDict> getBurialType() {
        // 获取埋点的字典配置得到不同模板下对应的埋点类型列表
        List<String> dictCodes = Arrays.asList(BurialStatisticsModel.ONE.getCode(), BurialStatisticsModel.TWO.getCode(), BurialStatisticsModel.THREE.getCode());
        return queryDict(dictCodes);
    }

    @Override
    public List<GeneratedMetaDict> dynamicsBurialType() {
        List<String> dictCodes = Collections.singletonList(BurialStatisticsModel.BURAIL_COMBINATION.getCode());
        return queryDict(dictCodes);
    }


    private AppStatisticsDayDTO createDayDTO(String dateKey,
                                             List<AppAnalysis> analyses,
                                             Map<Long, long[]> refIdSumMap,
                                             Map<String, GeneratedApplication> appInfoMap) {
        AppStatisticsDayDTO dayDTO = new AppStatisticsDayDTO();
        dayDTO.setDate(dateKey);

        // 获取一天的请求总量
        long totalRequestCount = analyses.stream().filter(appAnalysis -> null != refIdSumMap.get(appAnalysis.getId())).mapToLong(appAnalysis -> refIdSumMap.get(appAnalysis.getId())[0]).sum();

        Map<String, List<AppAnalysis>> appGroupMap = analyses.stream()
                .collect(Collectors.groupingBy(AppAnalysis::getAppId));


        List<AppStatisticsSummaryDTO> summaries = appGroupMap.entrySet()
                .stream()
                .map(e -> createSummaryDTO(e.getKey(), e.getValue(), refIdSumMap, appInfoMap, totalRequestCount))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        dayDTO.setDetails(summaries);
        return dayDTO;
    }

    private AppStatisticsSummaryDTO createSummaryDTO(String appId,
                                                     List<AppAnalysis> appAnalyses,
                                                     Map<Long, long[]> refIdSumMap,
                                                     Map<String, GeneratedApplication> appInfoMap,
                                                     long totalRequestCount) {
        long[] totalResults = appAnalyses.stream()
                .mapToLong(AppAnalysis::getId)
                .mapToObj(refIdSumMap::get)
                .filter(Objects::nonNull)
                .reduce(new long[2], (a, b) -> new long[]{a[0] + b[0], a[1] + b[1]});

        if (totalResults[0] == 0) return null; // 过滤无请求数据

        GeneratedApplication appInfo = appInfoMap.get(appId);
        if (appInfo == null) {
            return null;
        }

        AppStatisticsSummaryDTO dto = new AppStatisticsSummaryDTO();
        dto.setApp(appInfo.getName());
        dto.setRequestCount(totalResults[0]);
        dto.setHasResultCount(totalResults[1]);
        dto.setNoResultCount(totalResults[0] - totalResults[1]);
        dto.setHasResultPercentage(DataCalculateUtil.calculatePercentage(totalResults[1], totalResults[0]));
        dto.setTrafficPercentage(DataCalculateUtil.calculatePercentage(totalResults[0], totalRequestCount));
        return dto;
    }


    private List<AppAnalysis> searchAppAnalysisListBetweenDate(String beginDate, String endDate, String appIds, String regions, String productIds) {

        List<String> appIdList = null;
        List<String> regionList = null;
        List<Long> productList = null;
        if (StringUtils.isNotBlank(appIds)) {
            appIdList = Arrays.asList(appIds.split(","));
        }
        if (StringUtils.isNotBlank(regions)) {
            regionList = Arrays.asList(regions.split(","));
        }
        if (StringUtils.isNotBlank(productIds)) {
            productList = Arrays.stream(productIds.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }

        List<AppAnalysis> analysisList = appAnalysisRepository.searchListBetweenDate(beginDate, endDate, appIdList, regionList, productList);
        if (CollectionUtil.isEmpty(analysisList)) {
            return new ArrayList<>();
        }
        return analysisList;

    }

    private List<BurialAnalysis> searchBurialAnalysisListBetweenDate(String burialType, String beginDate, String endDate, String regions, String productIds, String appIds) {

        List<Long> productList = null;
        List<String> regionList = null;
        List<String> appIdList = null;
        if (StringUtils.isNotBlank(productIds)) {
            productList = Arrays.stream(productIds.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(regions)) {
            regionList = Arrays.asList(regions.split(","));
        }
        if (StringUtils.isNotBlank(appIds)) {
            appIdList = Arrays.asList(appIds.split(","));
        }
        List<BurialAnalysis> burialAnalyses = burialAnalysisRepository.searchListBetweenDate(burialType, beginDate, endDate, regionList, productList, appIdList);
        if (CollectionUtil.isEmpty(burialAnalyses)) {
            return new ArrayList<>();
        }
        return burialAnalyses;

    }


    private List<BurialDynamicsAnalysis> searchDynamicsBurialAnalysisListBetweenDate(String burialType, String beginDate, String endDate, String regions, String productIds, String appIds) {

        List<Long> productList = null;
        List<String> regionList = null;
        List<String> appIdList = null;
        if (StringUtils.isNotBlank(productIds)) {
            productList = Arrays.stream(productIds.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(regions)) {
            regionList = Arrays.asList(regions.split(","));
        }
        if (StringUtils.isNotBlank(appIds)) {
            appIdList = Arrays.asList(appIds.split(","));
        }
        List<BurialDynamicsAnalysis> burialAnalyses = burialDynamicsRepository.searchListBetweenDate(burialType, beginDate, endDate, regionList, productList, appIdList);
        if (CollectionUtil.isEmpty(burialAnalyses)) {
            return new ArrayList<>();
        }
        return burialAnalyses;

    }


    private List<FlowAnalysis> getFlowAnalysis(List<AppAnalysis> tempList) {
        if (CollectionUtil.isEmpty(tempList)) {
            return new ArrayList<>();
        }
        List<Long> refIds = tempList.stream().map(AppAnalysis::getId).collect(Collectors.toList());
        return flowAnalysisRepository.findByRefIdIn(refIds);
    }

    private List<FlowAnalysis> getBurialAnalysis(List<BurialAnalysis> tempList) {
        if (CollectionUtil.isEmpty(tempList)) {
            return new ArrayList<>();
        }
        List<Long> refIds = tempList.stream().map(BurialAnalysis::getId).collect(Collectors.toList());
        return flowAnalysisRepository.findByRefIdIn(refIds);
    }


    // 总请求量
    private Map<String, Object> buildStatisticMap(long count, long days) {
        Map<String, Object> map = new HashMap<>();
        map.put("count", count);
        map.put("avg", days > 0 ? count / days : 0);
        return map;
    }


    // 成功率
    private Map<String, Object> buildRateStatistics(long successCount, long total, String successName, String failName) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> numbers = new ArrayList<>();

        numbers.add(createStatEntry(successCount, successName));
        numbers.add(createStatEntry(total - successCount, failName));

        result.put("num", numbers);
        result.put("percentage", DataCalculateUtil.calculatePercentage(successCount, total));
        return result;
    }

    private Map<String, Object> createStatEntry(long value, String name) {
        Map<String, Object> entry = new HashMap<>();
        entry.put("value", value);
        entry.put("name", name);
        return entry;
    }


    // 有结果率,包含环比
    private Map<String, Object> buildHasResultStatistics(long hasResultCount, long totalCount,
                                                         String begin, String end, String appIds, String regions) {
        Map<String, Object> result = new HashMap<>();
        result.put("count", hasResultCount);
        result.put("total", totalCount);

        BigDecimal currentRate = DataCalculateUtil.calculatePercentage(hasResultCount, totalCount);
        result.put("percentage", currentRate);

        // 获取环比数据
        String[] previousPeriod = DateUtil.getPreviousPeriod(begin, end);
        List<AppAnalysis> analysisPreviousList = searchAppAnalysisListBetweenDate(previousPeriod[0], previousPeriod[1], appIds, regions, null);

        List<Long> previousRefIds = analysisPreviousList.stream().map(AppAnalysis::getId).collect(Collectors.toList());
        List<FlowAnalysis> previousFlowData = flowAnalysisRepository.findByRefIdIn(previousRefIds);

        BigDecimal previousRate = calculatePreviousRate(previousFlowData);
        Map<String, Object> trend = new HashMap<>();
        trend.put("name", "环比");
        trend.put("value", previousFlowData.isEmpty() ? BigDecimal.ZERO : currentRate.subtract(previousRate));

        result.put("trend", trend);
        return result;
    }

    private BigDecimal calculatePreviousRate(List<FlowAnalysis> previousFlowData) {
        if (previousFlowData.isEmpty()) {
            return BigDecimal.ZERO;
        }

        long prevTotal = previousFlowData.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
        long prevHasResult = previousFlowData.stream().mapToLong(FlowAnalysis::getHasResult).sum();
        return DataCalculateUtil.calculatePercentage(prevHasResult, prevTotal);
    }


    /**
     * @param analysisList  List<AppAnalysis>
     * @param refIdMap      Map<Long, List<FlowAnalysis>> refIdMap
     * @param totalRequests totalRequests
     * @return 统计数据
     */
    private List<Map<String, Object>> buildAppTrafficStats(List<AppAnalysis> analysisList,
                                                           Map<Long, List<FlowAnalysis>> refIdMap,
                                                           long totalRequests) {
        Map<String, List<AppAnalysis>> appGroup = analysisList.stream()
                .collect(Collectors.groupingBy(AppAnalysis::getAppId));

        Map<String, GeneratedApplication> appInfoMap = applicationRepository.findUnDeleted().stream()
                .collect(Collectors.toMap(GeneratedApplication::getAppId, t -> t));

        return appGroup.entrySet().stream().map(entry -> {
            String appId = entry.getKey();
            long appTotal = entry.getValue().stream()
                    .mapToLong(app -> refIdMap.getOrDefault(app.getId(), Collections.emptyList())
                            .stream().mapToLong(FlowAnalysis::getRequestCount).sum())
                    .sum();
            long hasResultCount = entry.getValue().stream()
                    .mapToLong(app -> refIdMap.getOrDefault(app.getId(), Collections.emptyList())
                            .stream().mapToLong(FlowAnalysis::getHasResult).sum())
                    .sum();

            Map<String, Object> stat = new HashMap<>();
            GeneratedApplication app = appInfoMap.get(appId);
            stat.put("name", (app != null ? app.getName() : "未知应用") + "(" + appId + ")");
            stat.put("value", appTotal);
            stat.put("hasResultCount", hasResultCount);
            stat.put("percentage", DataCalculateUtil.calculatePercentage(appTotal, totalRequests));
            return stat;
        }).collect(Collectors.toList());


    }


    // 检索请求趋势分析-总览
    private Map<String, Object> buildTrendOverview(long totalRequestCount, String beginDate, String endDate,
                                                   String appIds, String regions) {
        Map<String, Object> overview = new HashMap<>();

        // 1. 累计检索总量
        long grandTotal = flowAnalysisRepository.getRequestCountSum();
        overview.put("grandTotal", grandTotal);
        overview.put("conditionGrandTotal", totalRequestCount);

        // 2. 计算环比
        String[] previousPeriod = DateUtil.getPreviousPeriod(beginDate, endDate);
        List<AppAnalysis> analysisPreviousList = searchAppAnalysisListBetweenDate(previousPeriod[0], previousPeriod[1], appIds, regions, null);
        List<Long> previousRefIds = analysisPreviousList.stream().map(AppAnalysis::getId).collect(Collectors.toList());
        List<FlowAnalysis> previousFlowData = flowAnalysisRepository.findByRefIdIn(previousRefIds);
        Map<String, Object> trend = new HashMap<>();
        trend.put("name", "环比");
        if (!previousFlowData.isEmpty()) {
            long prevTotal = previousFlowData.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
            trend.put("value", DataCalculateUtil.calculateGrowthRate(prevTotal, totalRequestCount));
        } else {
            trend.put("value", BigDecimal.ZERO);
        }
        overview.put("trend", trend);

        return overview;
    }


    // 检索请求趋势分析- 趋势图
    private List<Map<String, Object>> buildTrendAnalysis(List<AppAnalysis> analysisList,
                                                         Map<Long, List<FlowAnalysis>> refIdMap,
                                                         String beginDate, String endDate) {
        // 使用函数式分组条件,按日期分组
        Function<AppAnalysis, String> groupKeyFunction = app ->
                DateUtil.isExactlyOneYearApart(beginDate, endDate) ?
                        app.getDate().substring(0, 7) :
                        app.getDate();

        // 按日期分组并按键值升序排序
        Map<String, List<AppAnalysis>> groupAppAnalysisMap = analysisList.stream()
                .collect(Collectors.groupingBy(groupKeyFunction))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey()) // 按键值升序排序
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        // 使用流式处理构建趋势数据
        List<Map<String, Object>> trends = groupAppAnalysisMap.entrySet().stream()
                .map(entry -> processDateGroup(entry.getKey(), entry.getValue(), refIdMap))
                .collect(Collectors.toList());


        return extractTrendData(trends, Arrays.asList("hasResultCount", "noResultCount", "requestCount", "hasResultPercentage"));
    }


    private Map<String, Object> processDateGroup(String date, List<AppAnalysis> analyses,
                                                 Map<Long, List<FlowAnalysis>> refIdMap) {
        long totals = analyses.stream()
                .mapToLong(app -> refIdMap.getOrDefault(app.getId(), Collections.emptyList())
                        .stream()
                        .mapToLong(FlowAnalysis::getRequestCount)
                        .sum())
                .sum();

        long totalHasResult = analyses.stream()
                .mapToLong(app -> refIdMap.getOrDefault(app.getId(), Collections.emptyList())
                        .stream()
                        .mapToLong(FlowAnalysis::getHasResult)
                        .sum())
                .sum();


        Map<String, Object> data = new HashMap<>();
        data.put("date", date);
        data.put("requestCount", totals);
        data.put("hasResultPercentage", DataCalculateUtil.calculatePercentage(totalHasResult, totals));
        data.put("hasResultCount", totalHasResult);
        data.put("noResultCount", totals - totalHasResult);
        return data;
    }

    private List<Map<String, Object>> extractTrendData(List<Map<String, Object>> trends, List<String> keys) {
        return trends.stream()
                .map(item -> {
                    Map<String, Object> entry = new HashMap<>();
                    entry.put("date", item.get("date"));
                    for (String key : keys) {
                        entry.put(key, item.get(key));
                    }
                    return entry;
                })
                .collect(Collectors.toList());
    }


    // 产品方案分析
    private List<StatisticalReportDTO> buildProductAnalysis(List<AppAnalysis> analysisList,
                                                            Map<Long, List<FlowAnalysis>> refIdMap,
                                                            long totalRequestCount,
                                                            String beginDate, String endDate) {
        // 批量获取所有需要的产品信息
        Set<Long> productIds = new HashSet<>();
        Set<Long> versionIds = new HashSet<>();

        List<StatisticalReportDTO> reportList = analysisList.stream()
                .collect(Collectors.groupingBy(AppAnalysis::getProductId))
                .entrySet().stream()
                .map(entry -> buildProductReport(entry.getKey(), entry.getValue(), refIdMap, totalRequestCount, productIds, versionIds, beginDate, endDate))
                .collect(Collectors.toList());

        // 批量获取元数据
        Map<Long, GeneratedFlow> flowMap = flowRepository.findByIdIn(new ArrayList<>(productIds)).stream()
                .collect(Collectors.toMap(GeneratedFlow::getId, Function.identity()));

        Map<Long, GeneratedFlowVersion> versionMap = flowVersionRepository.findByIdIn(new ArrayList<>(versionIds)).stream()
                .collect(Collectors.toMap(GeneratedFlowVersion::getId, Function.identity()));

        // 填充元数据
        return reportList.stream()
                .peek(dto -> enhanceWithMetadata(dto, flowMap, versionMap))
                .collect(Collectors.toList());
    }

    private StatisticalReportDTO buildProductReport(Long productId, List<AppAnalysis> analyses,
                                                    Map<Long, List<FlowAnalysis>> refIdMap,
                                                    long totalRequestCount,
                                                    Set<Long> productIds,
                                                    Set<Long> versionIds,
                                                    String beginTime,
                                                    String endTime) {
        productIds.add(productId);

        StatisticalReportDTO dto = new StatisticalReportDTO();
        dto.setId(productId);

        List<StatisticalReportDTO> versions = analyses.stream()
                .map(app -> buildVersionReport(app, refIdMap, versionIds))
                .toList();

        long total = versions.stream().mapToLong(StatisticalReportDTO::getRequestCount).sum();
        long totalHasResult = versions.stream().mapToLong(StatisticalReportDTO::getHasResultCount).sum();

        dto.setRequestCount(total);
        dto.setHasResultCount(totalHasResult);
        dto.setNoResultCount(total - totalHasResult);
        dto.setValue(total);
        dto.setHasResultPercentage(DataCalculateUtil.calculatePercentage(totalHasResult, total));
        dto.setTrafficPercentage(DataCalculateUtil.calculatePercentage(total, totalRequestCount));

        // 合并产品版本
        Map<Long, List<StatisticalReportDTO>> mergeMap = versions.stream().collect(Collectors.groupingBy(StatisticalReportDTO::getId));
        List<StatisticalReportDTO> son = new ArrayList<>();
        for (Map.Entry<Long, List<StatisticalReportDTO>> obj : mergeMap.entrySet()) {
            StatisticalReportDTO statisticalReportDTO = new StatisticalReportDTO();
            statisticalReportDTO.setId(obj.getKey());
            long tempTotal = obj.getValue().stream().mapToLong(StatisticalReportDTO::getRequestCount).sum();
            long tempHasResult = obj.getValue().stream().mapToLong(StatisticalReportDTO::getHasResultCount).sum();
            statisticalReportDTO.setRequestCount(tempTotal);
            statisticalReportDTO.setHasResultCount(tempHasResult);
            statisticalReportDTO.setNoResultCount(tempTotal - tempHasResult);
            statisticalReportDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage(tempHasResult, tempTotal));
            son.add(statisticalReportDTO);
        }
        dto.setSon(son);

        List<Map<String, Object>> trend = buildTrendAnalysis(analyses, refIdMap, beginTime, endTime);
        dto.setTrend(trend);

        return dto;
    }

    private StatisticalReportDTO buildVersionReport(AppAnalysis app,
                                                    Map<Long, List<FlowAnalysis>> refIdMap,
                                                    Set<Long> versionIds) {
        List<FlowAnalysis> flows = refIdMap.getOrDefault(app.getId(), Collections.emptyList());
        long requestCount = flows.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
        long hasResult = flows.stream().mapToLong(FlowAnalysis::getHasResult).sum();

        StatisticalReportDTO dto = new StatisticalReportDTO();
        dto.setId(app.getProductVersionId());
        versionIds.add(app.getProductVersionId());
        dto.setRequestCount(requestCount);
        dto.setHasResultCount(hasResult);
        dto.setNoResultCount(requestCount - hasResult);
        dto.setHasResultPercentage(DataCalculateUtil.calculatePercentage(hasResult, requestCount));
        return dto;
    }

    private void enhanceWithMetadata(StatisticalReportDTO dto,
                                     Map<Long, GeneratedFlow> flowMap,
                                     Map<Long, GeneratedFlowVersion> versionMap) {
        GeneratedFlow flow = flowMap.get(dto.getId());
        if (flow != null) {
            dto.setName(flow.getName());
            dto.setCode(flow.getCode());
        }

        dto.getSon().forEach(version -> {
            GeneratedFlowVersion ver = versionMap.get(version.getId());
            if (ver != null) {
                version.setVersion(String.format("%03d", ver.getVersion()));
                version.setName(ver.getName());
            }
        });
    }


    private StatisticsTileDTO getProductExportDTO(AppAnalysis appAnalysis, Map<Long, FlowAnalysis> refIdMap, Map<String, GeneratedApplication> appInfoMap,
                                                  Map<Long, GeneratedFlow> productInfoMap, Map<Long, GeneratedFlowVersion> productVerionInfoMap,
                                                  Map<String, GeneratedMetaRegion> regionMap,
                                                  long totalRequestCount) {
        StatisticsTileDTO statisticsTileDTO = new StatisticsTileDTO();
        statisticsTileDTO.setDate(appAnalysis.getDate());
        statisticsTileDTO.setApp(appInfoMap.get(appAnalysis.getAppId()).getName() + "(" + appAnalysis.getAppId() + ")");
        statisticsTileDTO.setProduct(productInfoMap.get(appAnalysis.getProductId()).getName() + "(" + productInfoMap.get(appAnalysis.getProductId()).getCode() + ")");
        statisticsTileDTO.setProductVersion(productVerionInfoMap.get(appAnalysis.getProductVersionId()).getName() + "(" + String.format("%03d", productVerionInfoMap.get(appAnalysis.getProductVersionId()).getVersion()) + ")");
        statisticsTileDTO.setRegion(regionMap.get(appAnalysis.getRegion()).getName());
        // 获取流量数据
        FlowAnalysis flowAnalysis = refIdMap.get(appAnalysis.getId());
        statisticsTileDTO.setRequestCount(flowAnalysis.getRequestCount());
        statisticsTileDTO.setHasResultCount(flowAnalysis.getHasResult());
        statisticsTileDTO.setNoResultCount(flowAnalysis.getRequestCount() - flowAnalysis.getHasResult());
        statisticsTileDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage(flowAnalysis.getHasResult(), flowAnalysis.getRequestCount()));
        statisticsTileDTO.setTrafficPercentage(DataCalculateUtil.calculatePercentage(flowAnalysis.getRequestCount(), totalRequestCount));

        return statisticsTileDTO;
    }


    private StatisticsTileDTO getBurialExportDTO(BurialAnalysis appAnalysis,
                                                 Map<Long, FlowAnalysis> refIdMap,
                                                 Map<Long, GeneratedFlow> productInfoMap,
                                                 long totalRequestCount) {
        StatisticsTileDTO statisticsTileDTO = new StatisticsTileDTO();
        statisticsTileDTO.setDate(appAnalysis.getDate());
        statisticsTileDTO.setProduct(productInfoMap.get(appAnalysis.getProductId()).getName() + "(" + productInfoMap.get(appAnalysis.getProductId()).getCode() + ")");
        statisticsTileDTO.setBurialItem(appAnalysis.getBurialItem());

        // 获取流量数据
        FlowAnalysis flowAnalysis = refIdMap.get(appAnalysis.getId());
        statisticsTileDTO.setRequestCount(flowAnalysis.getRequestCount());
        statisticsTileDTO.setHasResultCount(flowAnalysis.getHasResult());
        statisticsTileDTO.setNoResultCount(flowAnalysis.getRequestCount() - flowAnalysis.getHasResult());
        statisticsTileDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage(flowAnalysis.getHasResult(), flowAnalysis.getRequestCount()));
        statisticsTileDTO.setTrafficPercentage(DataCalculateUtil.calculatePercentage(flowAnalysis.getRequestCount(), totalRequestCount));

        return statisticsTileDTO;
    }

    private StatisticsTileDTO getDyBurialExportDTO(BurialDynamicsAnalysis appAnalysis,
                                                   Map<Long, GeneratedFlow> productInfoMap,
                                                   long totalRequestCount) {
        StatisticsTileDTO statisticsTileDTO = new StatisticsTileDTO();
        statisticsTileDTO.setDate(appAnalysis.getDate());
        statisticsTileDTO.setProduct(productInfoMap.get(Long.valueOf(appAnalysis.getProductId())).getName() + "(" + productInfoMap.get(Long.valueOf(appAnalysis.getProductId())).getCode() + ")");
        statisticsTileDTO.setBurialItemMap(appAnalysis.getBurialItem());
        statisticsTileDTO.setBurialItem(appAnalysis.getBurialCode());
        statisticsTileDTO.setRequestCount(appAnalysis.getRequestCount());
        statisticsTileDTO.setHasResultCount(appAnalysis.getHasResult());
        statisticsTileDTO.setNoResultCount(appAnalysis.getRequestCount() - appAnalysis.getHasResult());
        statisticsTileDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage(appAnalysis.getHasResult(), appAnalysis.getRequestCount()));
        statisticsTileDTO.setTrafficPercentage(DataCalculateUtil.calculatePercentage(appAnalysis.getRequestCount(), totalRequestCount));

        return statisticsTileDTO;
    }


    /**
     * 获取产品方案映射
     */
    private Map<Long, GeneratedFlow> getProductFlowMap(List<BurialAnalysis> burialAnalyses) {
        List<Long> productIds = burialAnalyses.stream()
                .map(BurialAnalysis::getProductId)
                .distinct()
                .toList();

        return flowRepository.findByIdIn(new ArrayList<>(productIds)).stream()
                .collect(Collectors.toMap(GeneratedFlow::getId, Function.identity()));
    }


    /**
     * 初始化埋点字典配置
     */
    private void initBurialDictConfig(Map<String, String> filedModelMap, Map<String, GeneratedMetaDict> nameDictMap) {
        MetaDictFeign metaDictFeign = lynxiaoFeignClientManager.buildWithRegion(MetaDictFeign.class, RegionCode.HF);
        Arrays.asList(BurialStatisticsModel.ONE.getCode(), BurialStatisticsModel.TWO.getCode(), BurialStatisticsModel.THREE.getCode()).forEach(t -> {
            ApiResponse apiResponse = metaDictFeign.getListByCode(t);
            List<GeneratedMetaDict> dicts = apiResponse.getPayload().getList("data", GeneratedMetaDict.class);
            if (!CollectionUtil.isEmpty(dicts)) {
                for (GeneratedMetaDict dict : dicts) {
                    // 埋点项code与模板的映射
                    filedModelMap.put(dict.getCode(), t);
                    // 埋点项code与字典映射
                    nameDictMap.put(dict.getCode(), dict);
                }
            }
        });
    }

    /**
     * 按模板处理埋点数据
     *
     * @param burialTypeMap 某种埋点类型code（如 flowName、scene等）埋点条件
     * @param filedModelMap 该埋点类型code对应的模板
     * @param nameDictMap   该埋点类型对应的字典配置，用于回显数据
     * @param flowMap       产品方案map， 用于回显数据
     * @param refIdMap      流量数据map
     * @param beginDate     开始时间
     * @param endDate       结束时间
     * @param resultList    组装的返回的结果
     * @param regions       区域
     * @param productIds    产品方案
     * @param appIds        业务应用
     */
    private void handleBurialDataByModel(Map<String, List<BurialAnalysis>> burialTypeMap,
                                         Map<String, String> filedModelMap, Map<String, GeneratedMetaDict> nameDictMap,
                                         Map<Long, GeneratedFlow> flowMap, Map<Long, FlowAnalysis> refIdMap,
                                         String beginDate, String endDate, List<Object> resultList,
                                         String regions, String productIds, String appIds) {

        burialTypeMap.forEach((burialType, analysisList) -> {
            String modelCode = filedModelMap.get(burialType);
            GeneratedMetaDict dict = nameDictMap.get(burialType);

            if (dict == null || modelCode == null) {
                return;
            }

            // 查找匹配的 BurialStatisticsModel 枚举值
            BurialStatisticsModel statisticsModel = Arrays.stream(BurialStatisticsModel.values())
                    .filter(m -> m.getCode().equals(modelCode))
                    .findFirst()
                    .orElse(null);

            JSONObject model = new JSONObject();
            model.put("name", dict.getName() + "(" + dict.getCode() + ")");

            List<Object> modelResult = new ArrayList<>();
            switch (statisticsModel) {
                case ONE:
                    model.put("model", 1);
                    statisticsUtil.handleFromTypeBurial(analysisList, flowMap, refIdMap, beginDate, endDate, modelResult);
                    break;
                case TWO:
                    model.put("model", 2);
                    // 获取环比数据
                    String[] previousPeriod = DateUtil.getPreviousPeriod(beginDate, endDate);
                    List<BurialAnalysis> burialPreviousList = searchBurialAnalysisListBetweenDate(null, previousPeriod[0], previousPeriod[1], regions, productIds, appIds);
                    Map<Long, List<BurialAnalysis>> productPreviousPeroidMap = burialPreviousList.stream().filter(t -> burialType.equals(t.getBurialType())).collect(Collectors.groupingBy(BurialAnalysis::getProductId));
                    statisticsUtil.handleFlowNameBurial(analysisList, flowMap, refIdMap, beginDate, endDate, modelResult, productPreviousPeroidMap);
                    break;
                case THREE:
                    model.put("model", 3);
                    statisticsUtil.handleSceneBurial(analysisList, flowMap, refIdMap, beginDate, endDate, modelResult);
                    break;
                default:
                    return;
            }
            model.put("data", modelResult);
            resultList.add(model);
        });
    }

    /**
     * 获取单个埋点分析组合的流量平铺数据
     *
     * @param burialType
     * @param beginDate
     * @param endDate
     * @param appIds
     * @param regions
     * @return
     */
    public List<StatisticsTileDTO> getBurialTileStatisticsTraffic(String burialType, String beginDate, String endDate, String appIds, String regions) {

        // 1. 数据获取
        List<BurialAnalysis> burialAnalysisList = searchBurialAnalysisListBetweenDate(burialType, beginDate, endDate, regions, null, appIds);
        if (CollectionUtils.isEmpty(burialAnalysisList)) {
            return new ArrayList<>();
        }
        List<FlowAnalysis> flowAnalysisList = getBurialAnalysis(burialAnalysisList);
        long totalRequestCount = flowAnalysisList.stream().mapToLong(FlowAnalysis::getRequestCount).sum();
        Map<Long, FlowAnalysis> refIdMap = flowAnalysisList.stream().collect(Collectors.toMap(FlowAnalysis::getRefId, t -> t));
        // 获取产品方案列表，方便后续组装名称、编码
        List<Long> productIds = burialAnalysisList.stream().map(BurialAnalysis::getProductId).collect(Collectors.toList());
        Map<Long, GeneratedFlow> productInfoMap = flowRepository.findByIdIn(productIds).stream()
                .collect(Collectors.toMap(GeneratedFlow::getId, t -> t));

        // 2. 组装平铺的数据
        List<StatisticsTileDTO> exportList = new ArrayList<>();
        for (BurialAnalysis burialAnalysis : burialAnalysisList) {
            exportList.add(getBurialExportDTO(burialAnalysis, refIdMap, productInfoMap, totalRequestCount));
        }
        return exportList;
    }

    /**
     * 获取动态埋点分析组合的流量平铺数据
     *
     * @param burialType
     * @param beginDate
     * @param endDate
     * @param appIds
     * @param regions
     * @return
     */
    public List<StatisticsTileDTO> getDynamicsBurialTileStatisticsTraffic(String burialType, String beginDate, String endDate, String appIds, String regions) {

        // 1. 数据获取
        List<BurialDynamicsAnalysis> burialAnalysisList = searchDynamicsBurialAnalysisListBetweenDate(burialType, beginDate, endDate, regions, null, appIds);
        if (CollectionUtils.isEmpty(burialAnalysisList)) {
            return new ArrayList<>();
        }
        long totalRequestCount = burialAnalysisList.stream().mapToLong(BurialDynamicsAnalysis::getRequestCount).sum();
        // 获取产品方案列表，方便后续组装名称、编码
        List<Long> productIds = burialAnalysisList.stream().map(item -> Long.parseLong(item.getProductId())).collect(Collectors.toList());
        Map<Long, GeneratedFlow> productInfoMap = flowRepository.findByIdIn(productIds).stream()
                .collect(Collectors.toMap(GeneratedFlow::getId, t -> t));

        // 2. 组装平铺的数据
        List<StatisticsTileDTO> exportList = new ArrayList<>();
        for (BurialDynamicsAnalysis burialAnalysis : burialAnalysisList) {
            exportList.add(getDyBurialExportDTO(burialAnalysis, productInfoMap, totalRequestCount));
        }
        return exportList;
    }


    private List<GeneratedMetaDict> queryDict(List<String> dictCodes) {
        List<GeneratedMetaDict> result = new ArrayList<>();
        MetaDictFeign metaDictFeign = lynxiaoFeignClientManager.buildWithRegion(MetaDictFeign.class, RegionCode.HF);
        dictCodes.forEach(t -> {
            ApiResponse apiResponse = metaDictFeign.getListByCode(t);
            List<GeneratedMetaDict> itemList = apiResponse.getPayload().getList("data", GeneratedMetaDict.class);
            if (!CollectionUtil.isEmpty(itemList)) {
                result.addAll(itemList);
            }
        });
        return result;
    }

}
