package com.iflytek.lynxiao.support.service.statistics;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.support.dto.common.GeneratedMetaDict;
import com.iflytek.lynxiao.support.dto.statistics.*;

import java.util.List;

/**
 * 平台流量统计
 * <AUTHOR>
 */
public interface PlatformStatisticsService {


    /**
     *
     * 获取业务应用流量
     * @param beginDate    yyyy-MM-dd
     * @param endDate      yyyy-MM-dd
     * @param appIds       appId集合，多个用英文逗号隔开
     * @param regions      region集合，多个用英文逗号隔开
     * @return             JSONObject
     */
    JSONObject getAppTraffic(String beginDate, String endDate, String appIds, String regions);

    /**
     *
     * 获取埋点流量
     * @param beginDate    yyyy-MM-dd
     * @param endDate      yyyy-MM-dd
     * @param appIds       appId集合，多个用英文逗号隔开
     * @param regions      region集合，多个用英文逗号隔开
     * @return             JSONObject
     */
    List<Object> getBurialTraffic(String beginDate, String endDate, String appIds, String regions, String productIds);

    /**
     * 获取环境流量分析
     * @param beginDate    yyyy-MM-dd
     * @param endDate      yyyy-MM-dd
     * @param appIds       业务应用集合，多个用英文逗号隔开
     * @return             JSONObject
     */
    JSONObject getEnvTraffic(String beginDate, String endDate, String appIds, String regions, String productIds);

    /**
     * 获取流量数据平铺结构
     * @param beginDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @param appIds    业务应用集合，多个用英文逗号隔开
     * @param regions   区域应用集合，多个用英文逗号隔开
     * @return          List<StatisticsTileDTO>
     */
    List<StatisticsTileDTO> getTileStatisticsTraffic(String beginDate, String endDate, String appIds, String regions);


    /**
     * 导出业务应用流量汇总表
     * @param beginDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @param appIds    业务应用集合，多个用英文逗号隔开
     * @param regions   区域应用集合，多个用英文逗号隔开
     * @return          List<AppStatisticsSummaryDTO>
     */
    List<AppStatisticsSummaryDTO> exportAppSummaryTraffic(String beginDate, String endDate, String appIds, String regions);

    /**
     * 导出业务应用流量(每日)
     * @param beginDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @param appIds    业务应用集合，多个用英文逗号隔开
     * @param regions   区域应用集合，多个用英文逗号隔开
     * @return          List<AppStatisticsDayDTO>
     */
    List<AppStatisticsDayDTO> exportAppDayTraffic(String beginDate, String endDate, String appIds, String regions);

    /**
     * 按环境日期段统计业务应用流量明细
     * @param beginDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @param appIds    业务应用集合，多个用英文逗号隔开
     * @param regions   区域应用集合，多个用英文逗号隔开
     * @return          List<AppStatisticsDayEnvDTO>
     */
    List<AppStatisticsDayEnvDTO> exportAppDayEnvTraffic(String beginDate, String endDate, String appIds, String regions);


    /**
     * 按环境日期段统计产品方案流量明细
     * @param beginDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @param appIds    业务应用集合，多个用英文逗号隔开
     * @param regions   区域应用集合，多个用英文逗号隔开
     * @return          List<ProductEnvStatisticsDTO>
     */
    List<ProductEnvStatisticsDTO> exportProductDayEnvTraffic(String beginDate, String endDate, String appIds, String regions);

    /**
     * 产品方案流量分析日报表
     * @param beginDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @param appIds    业务应用集合，多个用英文逗号隔开
     * @param regions   区域应用集合，多个用英文逗号隔开
     * @return          List<ProductStatisticsDTO>
     */
    List<ProductStatisticsDTO> exportProductDayTraffic(String beginDate, String endDate, String appIds, String regions);

    /**
     * 埋点日报表
     * @param burialType 埋点类型
     * @param beginDate yyyy-MM-dd
     * @param endDate   yyyy-MM-dd
     * @param appIds    业务应用集合，多个用英文逗号隔开
     * @param regions   区域应用集合，多个用英文逗号隔开
     * @return          List<BurialDTO>
     */
    List<BurialDTO> exportBurialTraffic(String burialType, String beginDate, String endDate, String appIds, String regions);

    /**
     * 埋点组合分析日报表
     * @param burialType 埋点类型(多种埋点类型的组合)
     * @param beginDate  yyyy-MM-dd
     * @param endDate    yyyy-MM-dd
     * @param appIds     业务应用集合，多个用英文逗号隔开
     * @param regions    区域应用集合，多个用英文逗号隔开
     * @return           List<BurialDTO>
     */
    List<BurialDTO> exportDynamicsBurialTraffic(String burialType, String beginDate, String endDate, String appIds, String regions);

    List<StatisticsTileDTO> exportTileDynamicsBurialTraffic(String burialType, String beginDate, String endDate, String appIds, String regions);


    /**
     * 查询埋点类型列表
     * @return List<GeneratedMetaDict>
     */
    List<GeneratedMetaDict> getBurialType();


    /**
     * 查询动态埋点类型列表
     * @return List<GeneratedMetaDict>
     */
    List<GeneratedMetaDict> dynamicsBurialType();


}
