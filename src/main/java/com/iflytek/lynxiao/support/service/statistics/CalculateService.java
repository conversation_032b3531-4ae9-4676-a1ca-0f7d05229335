package com.iflytek.lynxiao.support.service.statistics;

/**
 * <AUTHOR>
 *
 * 流量分析计算服务
 */
public interface CalculateService {

    /**
     * 生成业务应用流量
     * @param date yyyy-MM-dd
     */
    void statisticsAppTraffic(String date);

    /**
     * 生成埋点分析流量
     * @param date yyyy-MM-dd
     */
    void statisticsBurialTraffic(String date);

    /**
     * 生成动态埋点分析流量
     * @param date yyyy-MM-dd
     */
    void statisticsDynamicsBurial(String date);

    /**
     * 生成业务唯一标识分析流量
     * @param date yyyy-MM-dd
     */
    void statisticsBizEqualId(String date);

}
