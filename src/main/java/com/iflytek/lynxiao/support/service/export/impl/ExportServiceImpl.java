package com.iflytek.lynxiao.support.service.export.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.support.dto.MailStatisticsConditionDTO;
import com.iflytek.lynxiao.support.dto.export.ExportDTO;
import com.iflytek.lynxiao.support.dto.export.ExportParamDTO;
import com.iflytek.lynxiao.support.dto.export.QueryDocListDTO;
import com.iflytek.lynxiao.support.service.elastic.ElasticSearchService;
import com.iflytek.lynxiao.support.service.export.ExportService;
import com.iflytek.lynxiao.support.util.StatisticsUtil;
import jakarta.annotation.Resource;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
public class ExportServiceImpl implements ExportService {

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Resource
    private StatisticsUtil statisticsUtil;


    private final String indexPre = "lynxiao_flow-";

    @Override
    public List<ExportDTO> exportQuery(ExportParamDTO dto) {
        List<MailStatisticsConditionDTO> flowAnalysisCondition = getCondition(dto);
        List<QueryBuilder> queryBuilders = elasticSearchService.recursionCondition(JSON.toJSONString(flowAnalysisCondition));
        List<JSONObject> results = elasticSearchService.getAllDocumentsByScrollWithMax(dto.getRegion(), indexPre + dto.getDate(), queryBuilders, TimeValue.timeValueMinutes(2), dto.getLimit(), dto.getMax());
        if (CollectionUtil.isEmpty(results)) {
            return new ArrayList<>();
        }
        List<ExportDTO> exportList = new ArrayList<>();
        for (JSONObject obj: results) {
            ExportDTO exportDTO = new ExportDTO();
            if (StringUtils.isBlank(getQuery(obj))) {
                continue;
            }
            exportDTO.setQuery(getQuery(obj));
            exportList.add(exportDTO);
        }
        return exportList;
    }

    @Override
    public List<QueryDocListDTO> exportQueryDocList(ExportParamDTO dto) {
        List<MailStatisticsConditionDTO> flowAnalysisCondition = getCondition(dto);
        List<QueryBuilder> queryBuilders = elasticSearchService.recursionCondition(JSON.toJSONString(flowAnalysisCondition));
        List<JSONObject> results = elasticSearchService.getAllDocumentsByScrollWithMax(dto.getRegion(), indexPre + dto.getDate(), queryBuilders, TimeValue.timeValueMinutes(2), dto.getLimit(), dto.getMax());
        if (CollectionUtil.isEmpty(results)) {
            return new ArrayList<>();
        }
        List<QueryDocListDTO> exportList = new ArrayList<>();
        for (JSONObject obj: results) {
            if (null == getQueryAndDoc(obj)) {
                continue;
            }
            exportList.add(getQueryAndDoc(obj));
        }
        return exportList;
    }


    private  List<MailStatisticsConditionDTO> getCondition(ExportParamDTO dto) {
        List<MailStatisticsConditionDTO> result = new ArrayList<>();

        // type
        MailStatisticsConditionDTO type = new MailStatisticsConditionDTO("type.keyword", "EQ", "SearchAPI-Response");
        result.add(type);


        // flowName
        if (StringUtils.isNotBlank(dto.getFlowName())) {
            MailStatisticsConditionDTO flowName = new MailStatisticsConditionDTO( "flowName", "CONT", dto.getFlowName());
            result.add(flowName);
        }

        if (StringUtils.isNotBlank(dto.getAppIds())) {
            MailStatisticsConditionDTO appIdCondition = new MailStatisticsConditionDTO("appId", "IN", dto.getAppIds());
            result.add(appIdCondition);
        }
        // 有无结果
        if (null != dto.getHasResult()) {
            if (dto.getHasResult()) {
                MailStatisticsConditionDTO hasResult = new MailStatisticsConditionDTO("docsCount", "GT", "0");
                result.add(hasResult);
            } else {
                MailStatisticsConditionDTO hasResult = new MailStatisticsConditionDTO("docsCount", "NO_RESULT", "0");
                result.add(hasResult);
            }
        }

        return result;

    }


    private String getQuery(JSONObject source){

        try {

            JSONObject data = source.getJSONObject("data");
            JSONObject payload = data.getJSONObject("payload");
            JSONObject output = payload.getJSONObject("output");
            JSONArray dataList = output.getJSONArray("data");
            if (null == dataList) {
                return StringUtils.EMPTY;
            }
            JSONObject one = dataList.getJSONObject(0);
            System.out.println(one.getString("query"));
            return one.getString("query");
        }catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return StringUtils.EMPTY;
    }

    private QueryDocListDTO getQueryAndDoc(JSONObject source){

        try {

            QueryDocListDTO queryDocListDTO = new QueryDocListDTO();
            JSONObject data = source.getJSONObject("data");
            JSONObject payload = data.getJSONObject("payload");
            JSONObject output = payload.getJSONObject("output");
            JSONArray dataList = output.getJSONArray("data");
            if (null == dataList) {
                return null;
            }
            JSONObject one = dataList.getJSONObject(0);
            queryDocListDTO.setQuery(one.getString("query"));
            List<QueryDocListDTO.Doc> docs = new ArrayList<>();
            JSONArray docsResult = one.getJSONArray("docs");
            if (CollectionUtil.isEmpty(docsResult)) {
                queryDocListDTO.setDocs(docs);
                return queryDocListDTO;
            }
            for (int i = 0; i < docsResult.size(); i++) {
                JSONObject doc = docsResult.getJSONObject(i);

                QueryDocListDTO.Doc sourceDoc = new QueryDocListDTO.Doc();
                sourceDoc.setId(doc.getString("id"));
                sourceDoc.setUrl(doc.getString("url"));
                sourceDoc.setTitle(doc.getString("title"));
                sourceDoc.setContent(doc.getString("content"));
                sourceDoc.setRankScore(doc.getBigDecimal("rank_score"));
                sourceDoc.setReRankScore(doc.getBigDecimal("rerank_score"));
                sourceDoc.setIndex(doc.getInteger("index"));
                sourceDoc.setIndexCode(doc.getString("indexCode"));
                docs.add(sourceDoc);
            }

            queryDocListDTO.setDocs(docs);
            return queryDocListDTO;
        }catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

}
