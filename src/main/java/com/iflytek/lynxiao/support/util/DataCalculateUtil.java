package com.iflytek.lynxiao.support.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * <AUTHOR>
 * 数据计算工具类
 */
public class DataCalculateUtil {

    // 计算占比
    public static BigDecimal calculatePercentage(long long1, long long2) {
        if (long2 == 0) {
            return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_DOWN);
        }

        BigDecimal bigDecimal1 = BigDecimal.valueOf(long1);
        BigDecimal bigDecimal2 = BigDecimal.valueOf(long2);
        BigDecimal percentage = bigDecimal1.divide(bigDecimal2, 4, RoundingMode.HALF_DOWN).multiply(BigDecimal.valueOf(100));

        return percentage.setScale(2, RoundingMode.HALF_DOWN);
    }


    // 计算增长率
    public static BigDecimal calculateGrowthRate(long previousValue, long currentValue) {
        // 计算差值
        long difference = currentValue - previousValue;

        // 使用BigDecimal进行计算
        BigDecimal differenceBigDecimal = BigDecimal.valueOf(difference);
        BigDecimal previousValueBigDecimal = BigDecimal.valueOf(previousValue);

        // 计算增长率
        return differenceBigDecimal.divide(previousValueBigDecimal, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }


    public static String getFormatNumber(Long number) {
        if (null == number || 0 == number) {
            return "0";
        }

        try {
            NumberFormat numberFormat = NumberFormat.getNumberInstance(Locale.US);
            return numberFormat.format(number);
        } catch (Exception e) {
            return number.toString();
        }

    }





}
