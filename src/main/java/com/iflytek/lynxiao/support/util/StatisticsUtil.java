package com.iflytek.lynxiao.support.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.support.consts.RegionCode;
import com.iflytek.lynxiao.support.dto.statistics.StatisticalReportDTO;
import com.iflytek.lynxiao.support.repository.portal.entity.GeneratedFlow;
import com.iflytek.lynxiao.support.repository.support.FlowAnalysisRepository;
import com.iflytek.lynxiao.support.repository.support.entity.BurialAnalysis;
import com.iflytek.lynxiao.support.repository.support.entity.FlowAnalysis;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 平台流量分析统计工具类
 *
 * <AUTHOR>
 */
@Service
public class StatisticsUtil {

    @Resource
    private FlowAnalysisRepository flowAnalysisRepository;

    /**
     * 用于将医疗标签按层级分组
     * 比如 level=1 , 医疗标签为：诊疗-治疗方案-中医治疗方案，则只按照 诊疗  分组得到 List<BurialAnalysis>
     *
     * @param level
     * @param list
     * @return
     */
    public Map<String, List<BurialAnalysis>> groupTreatmentsByLevel(int level, List<BurialAnalysis> list) {
        return list.stream()
                .collect(Collectors.groupingBy(t -> {
                    String[] parts = t.getBurialItem().split("-");
                    if (parts.length >= level) {
                        return String.join("-", Arrays.copyOfRange(parts, 0, level));
                    } else {
                        return String.join("-", Arrays.copyOfRange(parts, 0, parts.length));
                    }
                }));
    }


    /**
     * 处理时间趋势图逻辑
     */
    private void handleTimeTrend(List<BurialAnalysis> productAnalysisList, Map<Long, FlowAnalysis> refIdMap,
                                 String beginDate, String endDate, JSONObject resultObj) {
        Function<BurialAnalysis, String> groupKeyFunction = app ->
                DateUtil.isExactlyOneYearApart(beginDate, endDate) ?
                        app.getDate().substring(0, 7) :
                        app.getDate();

        Map<String, List<BurialAnalysis>> groupBurialAnalysisMap = productAnalysisList.stream()
                .collect(Collectors.groupingBy(groupKeyFunction))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey()) // 按键值升序排序
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        List<Object> trendList = new ArrayList<>();
        groupBurialAnalysisMap.forEach((date, tempDateAnalysisList) -> {
            JSONObject trendObj = new JSONObject();
            trendObj.put("date", date);

            Map<String, List<BurialAnalysis>> tempTypeMap = tempDateAnalysisList.stream()
                    .collect(Collectors.groupingBy(BurialAnalysis::getBurialItem));

            List<Object> typeNumList = new ArrayList<>();
            tempTypeMap.forEach((type, tempTypeAnalysis) -> {
                JSONObject typeNumObj = new JSONObject();
                typeNumObj.put("name", type);

                long tempNumRequestCount = 0; // 请求总数
                long temHasResultRequestCount = 0;
                for (BurialAnalysis analysis : tempTypeAnalysis) {
                    tempNumRequestCount += refIdMap.get(analysis.getId()).getRequestCount();
                    temHasResultRequestCount += refIdMap.get(analysis.getId()).getHasResult();
                }

                typeNumObj.put("value", tempNumRequestCount);
                typeNumObj.put("hasResultPercentage", DataCalculateUtil.calculatePercentage(temHasResultRequestCount, tempNumRequestCount));
                typeNumList.add(typeNumObj);
            });

            trendObj.put("num", typeNumList);
            trendList.add(trendObj);
        });

        resultObj.put("trend", trendList);
    }


    /**
     * 处理来源类型埋点逻辑(model=1)
     */
    public void handleFromTypeBurial(List<BurialAnalysis> list,
                                     Map<Long, GeneratedFlow> flowMap, Map<Long, FlowAnalysis> refIdMap,
                                     String beginDate, String endDate, List<Object> fromTypeResult) {
        // 按产品分组处理
        list.stream()
                .collect(Collectors.groupingBy(BurialAnalysis::getProductId))
                .forEach((productId, productAnalysisList) -> {
                    JSONObject fromTypeObj = generateBaseObject(flowMap, productId, productAnalysisList, refIdMap);

                    // 按埋点项分组处理
                    handleBurialItemsForFromType(productAnalysisList, refIdMap, fromTypeObj);

                    // 处理时间趋势图
                    handleTimeTrend(productAnalysisList, refIdMap, beginDate, endDate, fromTypeObj);

                    fromTypeResult.add(fromTypeObj);
                });
    }

    /**
     * 处理流程名称埋点逻辑(model=2)
     */
    public void handleFlowNameBurial(List<BurialAnalysis> list,
                                     Map<Long, GeneratedFlow> flowMap,
                                     Map<Long, FlowAnalysis> refIdMap,
                                     String beginDate,
                                     String endDate,
                                     List<Object> flowNameResult,
                                     Map<Long, List<BurialAnalysis>> productPreviousPeroidMap) {

        // 数据统计
        // 按产品分组处理
        list.stream()
                .collect(Collectors.groupingBy(BurialAnalysis::getProductId))
                .forEach((productId, productAnalysisList) -> {
                    JSONObject flowNameObj = generateBaseObject(flowMap, productId, productAnalysisList, refIdMap);

                    List<BurialAnalysis> productPreviousAnalysisList = productPreviousPeroidMap.get(productId);
                    // 按埋点项分组处理
                    handleBurialItemsForFlowName(productAnalysisList, productPreviousAnalysisList, refIdMap, flowNameObj);

                    // 处理时间趋势图
                    handleTimeTrend(productAnalysisList, refIdMap, beginDate, endDate, flowNameObj);

                    flowNameResult.add(flowNameObj);
                });
    }


    /**
     * 处理医疗场景标签埋点逻辑(model=3)
     */
    public void handleSceneBurial(List<BurialAnalysis> list,
                                  Map<Long, GeneratedFlow> flowMap, Map<Long, FlowAnalysis> refIdMap,
                                  String beginDate, String endDate, List<Object> sceneResult) {
        // 按产品分组处理
        list.stream()
                .collect(Collectors.groupingBy(BurialAnalysis::getProductId))
                .forEach((productId, productAnalysisList) -> {

                    Map<String, List<BurialAnalysis>> oneLevel = groupTreatmentsByLevel(1, productAnalysisList);
                    Map<String, List<BurialAnalysis>> twoLevel = groupTreatmentsByLevel(2, productAnalysisList);
                    Map<String, List<BurialAnalysis>> threeLevel = groupTreatmentsByLevel(3, productAnalysisList);

                    JSONObject sceneObj = generateBaseObject(flowMap, productId, productAnalysisList, refIdMap);

                    long requestCount = sceneObj.getLong("requestCount");
                    // 按埋点项分组处理(标签分为层级维度)
                    JSONObject oneLevelObj = handleBurialItemsForScene(oneLevel, refIdMap, beginDate, endDate, requestCount);
                    sceneObj.put("oneLevel", oneLevelObj);
                    JSONObject twoLevelObj = handleBurialItemsForScene(twoLevel, refIdMap, beginDate, endDate, requestCount);
                    sceneObj.put("twoLevel", twoLevelObj);
                    JSONObject threeLevelObj = handleBurialItemsForScene(threeLevel, refIdMap, beginDate, endDate, requestCount);
                    sceneObj.put("threeLevel", threeLevelObj);

                    sceneResult.add(sceneObj);
                });
    }

    /**
     * 处理埋点项分组逻辑（医疗标签埋点）
     */
    public JSONObject handleBurialItemsForScene(Map<String, List<BurialAnalysis>> sceneAnalysisMap,
                                                Map<Long, FlowAnalysis> refIdMap,
                                                String beginDate,
                                                String endDate,
                                                long requestCount) {

        JSONObject result = new JSONObject();

        // 饼图数据
        List<Object> num = new ArrayList<>();
        // 总览数据
        List<StatisticalReportDTO> overviewList = new ArrayList<>();
        // 时间趋势图数据
        List<Map<String, Object>> trendList = new ArrayList<>();
        // key: date_scene   value:请求统计对象
        Map<String, StatisticalReportDTO> dateSceneCountMap = new LinkedHashMap<>();

        // 按照场景标签分级维度统计
        sceneAnalysisMap.forEach((scene, productAnalysisList) -> {
            // 饼图和总览数据
            processPieAndOverviewData(scene, productAnalysisList, refIdMap, num, overviewList, requestCount);

            // 时间趋势图数据
            processTrendData(scene, productAnalysisList, refIdMap, beginDate, endDate, dateSceneCountMap);
        });

        // 转换时间趋势图数据格式
        convertTrendDataFormat(dateSceneCountMap, trendList);

        result.put("num", num);
        result.put("overview", overviewList);
        result.put("trend", trendList);

        return result;
    }


    /**
     * 转换时间趋势图数据格式
     */
    private void convertTrendDataFormat(Map<String, StatisticalReportDTO> dateSceneCountMap, List<Map<String, Object>> trendList) {
        // 按日期分组并转换格式
        Map<String, List<Map.Entry<String, StatisticalReportDTO>>> groupedEntries = dateSceneCountMap.entrySet().stream()
                .collect(Collectors.groupingBy(entry -> entry.getKey().split("_")[0]))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey()) // 按键值升序排序
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));


        groupedEntries.forEach((date, entries) -> {
            Map<String, Object> dateMap = new HashMap<>();
            dateMap.put("date", date);

            List<Map<String, Object>> numList = entries.stream()
                    .map(entry -> {
                        String[] parts = entry.getKey().split("_");
                        Map<String, Object> numMap = new HashMap<>();
                        numMap.put("value", entry.getValue().getRequestCount());
                        numMap.put("hasResultPercentage", entry.getValue().getHasResultPercentage());
                        numMap.put("name", parts[1]);
                        return numMap;
                    })
                    .collect(Collectors.toList());

            dateMap.put("num", numList);
            trendList.add(dateMap);
        });
    }


    /**
     * 处理饼图和总览数据
     */
    private void processPieAndOverviewData(String scene, List<BurialAnalysis> productAnalysisList,
                                           Map<Long, FlowAnalysis> refIdMap, List<Object> num,
                                           List<StatisticalReportDTO> overviewList, long requestCount) {
        long totalRequestCount = 0;
        long totalHasCountResult = 0;

        for (BurialAnalysis analysis : productAnalysisList) {
            FlowAnalysis flowAnalysis = refIdMap.get(analysis.getId());
            totalRequestCount += flowAnalysis.getRequestCount();
            totalHasCountResult += flowAnalysis.getHasResult();
        }

        // 饼图数据
        JSONObject numObj = new JSONObject();
        numObj.put("name", scene);
        numObj.put("value", totalRequestCount);
        num.add(numObj);

        // 总览数据
        StatisticalReportDTO statisticalReportDTO = new StatisticalReportDTO();
        statisticalReportDTO.setName(scene);
        statisticalReportDTO.setRequestCount(totalRequestCount);
        statisticalReportDTO.setHasResultCount(totalHasCountResult);
        statisticalReportDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage(totalHasCountResult, totalRequestCount));
        statisticalReportDTO.setNoResultCount(totalRequestCount - totalHasCountResult);
        statisticalReportDTO.setTrafficPercentage(DataCalculateUtil.calculatePercentage(totalRequestCount, requestCount));
        overviewList.add(statisticalReportDTO);
    }


    /**
     * 生成基础对象
     */
    private JSONObject generateBaseObject(Map<Long, GeneratedFlow> flowMap, Long productId,
                                          List<BurialAnalysis> productAnalysisList, Map<Long, FlowAnalysis> refIdMap) {
        JSONObject resultObj = new JSONObject();
        GeneratedFlow flow = flowMap.get(productId);
        resultObj.put("product", flow.getName() + "(" + flow.getCode() + ")");
        long productRequestCount = productAnalysisList.stream()
                .mapToLong(analysis -> refIdMap.get(analysis.getId()).getRequestCount())
                .sum();
        resultObj.put("requestCount", productRequestCount);
        return resultObj;
    }

    /**
     * 处理埋点项分组逻辑（来源类型埋点）
     */
    private void handleBurialItemsForFromType(List<BurialAnalysis> productAnalysisList, Map<Long, FlowAnalysis> refIdMap, JSONObject fromTypeObj) {
        Map<String, List<BurialAnalysis>> burialItemMap = productAnalysisList.stream()
                .collect(Collectors.groupingBy(BurialAnalysis::getBurialItem));

        List<Object> numList = new ArrayList<>();
        List<StatisticalReportDTO> overviewList = new ArrayList<>();

        // 每个埋点流量指标
        burialItemMap.forEach((burialItem, tempItemList) -> {
            JSONObject numObj = new JSONObject();
            numObj.put("name", burialItem);

            long tempRequestCount = 0;
            long tempHasResultRequestCount = 0;
            for (BurialAnalysis analysis : tempItemList) {
                FlowAnalysis flowAnalysis = refIdMap.get(analysis.getId());
                tempRequestCount += flowAnalysis.getRequestCount();
                tempHasResultRequestCount += flowAnalysis.getHasResult();
            }

            numObj.put("value", tempRequestCount);
            numList.add(numObj);

            StatisticalReportDTO reportDTO = new StatisticalReportDTO();
            reportDTO.setName(burialItem);
            reportDTO.setRequestCount(tempRequestCount);
            reportDTO.setHasResultCount(tempHasResultRequestCount);
            reportDTO.setNoResultCount(tempRequestCount - tempHasResultRequestCount);
            reportDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage(tempHasResultRequestCount, tempRequestCount));
            reportDTO.setTrafficPercentage(DataCalculateUtil.calculatePercentage(tempRequestCount, fromTypeObj.getLong("requestCount")));
            overviewList.add(reportDTO);
        });

        fromTypeObj.put("num", numList);
        fromTypeObj.put("overview", overviewList);
    }

    /**
     * 处理埋点项分组逻辑（流程名称埋点）
     */
    private void handleBurialItemsForFlowName(List<BurialAnalysis> productAnalysisList,
                                              List<BurialAnalysis> productPreviousAnalysisList,
                                              Map<Long, FlowAnalysis> refIdMap,
                                              JSONObject flowNameObj) {
        Map<String, List<BurialAnalysis>> burialItemMap = productAnalysisList.stream()
                .collect(Collectors.groupingBy(BurialAnalysis::getBurialItem));

        Map<String, List<BurialAnalysis>> previousBurialItemMap =
                (productPreviousAnalysisList != null ? productPreviousAnalysisList : Collections.<BurialAnalysis>emptyList())
                        .stream()
                        .collect(Collectors.groupingBy(BurialAnalysis::getBurialItem));

        List<FlowAnalysis> flowAnalysisList = getBurialAnalysis(productPreviousAnalysisList);
        Map<Long, FlowAnalysis> previousRefIdMap = flowAnalysisList.stream()
                .collect(Collectors.toMap(FlowAnalysis::getRefId, Function.identity()));

        List<Object> overviewList = new ArrayList<>();

        burialItemMap.forEach((burialItem, tempItemList) -> {
            JSONObject itemObj = new JSONObject();
            itemObj.put("name", burialItem);

            long tempRequestCount = 0;
            long tempHasResultRequestCount = 0;
            // 统计请求数量
            for (BurialAnalysis analysis : tempItemList) {
                FlowAnalysis flowAnalysis = refIdMap.get(analysis.getId());
                tempRequestCount += flowAnalysis.getRequestCount();
                tempHasResultRequestCount += flowAnalysis.getHasResult();
            }

            // 饼图
            List<Object> numObjList = new ArrayList<>();
            JSONObject itemRequestCount = new JSONObject();
            itemRequestCount.put("name", "检索个数");
            itemRequestCount.put("value", tempRequestCount);
            numObjList.add(itemRequestCount);

            JSONObject requestCount = new JSONObject();
            requestCount.put("name", "剩余总数");
            requestCount.put("value", flowNameObj.getLong("requestCount") - tempRequestCount);
            numObjList.add(requestCount);

            itemObj.put("num", numObjList);
            // 总览
            itemObj.put("trafficPercentage", DataCalculateUtil.calculatePercentage(tempRequestCount, flowNameObj.getLong("requestCount")));
            itemObj.put("hasResultCount", tempHasResultRequestCount);
            itemObj.put("requestCount", tempRequestCount);
            BigDecimal hasResultPercentage = DataCalculateUtil.calculatePercentage(tempHasResultRequestCount, tempRequestCount);
            itemObj.put("hasResultPercentage", hasResultPercentage);

            // 计算环比
            long previousTempRequestCount = 0;
            long previousTempHasResultRequestCount = 0;
            if (CollectionUtil.isNotEmpty(previousBurialItemMap.get(burialItem))) {
                for (BurialAnalysis analysis : previousBurialItemMap.get(burialItem)) {
                    FlowAnalysis flowAnalysis = previousRefIdMap.get(analysis.getId());
                    if (null == flowAnalysis) {
                        continue;
                    }
                    previousTempRequestCount += flowAnalysis.getRequestCount();
                    previousTempHasResultRequestCount += flowAnalysis.getHasResult();
                }
            }
            BigDecimal previousHasResultPercentage = DataCalculateUtil.calculatePercentage(previousTempHasResultRequestCount, previousTempRequestCount);
            JSONObject trend = new JSONObject();
            trend.put("name", "环比");
            trend.put("value", hasResultPercentage.subtract(previousHasResultPercentage));
            itemObj.put("trend", trend);

            overviewList.add(itemObj);
        });

        flowNameObj.put("overview", overviewList);
    }


    /**
     * 处理时间趋势图数据(针对医疗标签)
     */
    private void processTrendData(String scene, List<BurialAnalysis> productAnalysisList,
                                  Map<Long, FlowAnalysis> refIdMap, String beginDate, String endDate,
                                  Map<String, StatisticalReportDTO> dateSceneCountMap) {
        // 确定分组键函数
        Function<BurialAnalysis, String> groupKeyFunction = app ->
                DateUtil.isExactlyOneYearApart(beginDate, endDate) ?
                        app.getDate().substring(0, 7) : // 按年月分组
                        app.getDate(); // 按日期分组

        // 按日期分组
        Map<String, List<BurialAnalysis>> groupBurialAnalysisMap = productAnalysisList.stream()
                .collect(Collectors.groupingBy(groupKeyFunction))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey()) // 按键值升序排序
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        // 统计每个日期的请求次数
        groupBurialAnalysisMap.forEach((date, analyses) -> {
            long dateRequestCount = analyses.stream()
                    .mapToLong(analysis -> refIdMap.get(analysis.getId()).getRequestCount())
                    .sum();
            long dateHasResultRequestCount = analyses.stream()
                    .mapToLong(analysis -> refIdMap.get(analysis.getId()).getHasResult())
                    .sum();
            StatisticalReportDTO statisticalReportDTO = new StatisticalReportDTO();
            statisticalReportDTO.setRequestCount(dateRequestCount);
            statisticalReportDTO.setHasResultCount(dateHasResultRequestCount);
            statisticalReportDTO.setHasResultPercentage(DataCalculateUtil.calculatePercentage(dateHasResultRequestCount, dateRequestCount));
            dateSceneCountMap.put(date + "_" + scene, statisticalReportDTO);
        });
    }

    private List<FlowAnalysis> getBurialAnalysis(List<BurialAnalysis> tempList) {
        if (CollectionUtil.isEmpty(tempList)) {
            return new ArrayList<>();
        }
        List<Long> refIds = tempList.stream().map(BurialAnalysis::getId).collect(Collectors.toList());
        return flowAnalysisRepository.findByRefIdIn(refIds);
    }
}