package com.iflytek.lynxiao.support.util;


import com.alibaba.fastjson2.JSON;
import com.iflytek.lynxiao.support.dto.statistics.StatisticsTileDTO;
import com.iflytek.lynxiao.support.dto.statistics.ProductStatisticsDTO;


import java.util.*;

/**
 * <AUTHOR>
 * 测试类
 */
public class Test {

    public static void main(String[] args) {
        // 模拟数据
        List<StatisticsTileDTO> dataList = new ArrayList<>();
        dataList.add(new StatisticsTileDTO("2024-10-10", "hf", "App1", "ProductA", "2.0.0", 100, 80, 20, null, null, "", null));
        dataList.add(new StatisticsTileDTO("2024-10-10","hf", "App1", "ProductA", "1.0.0", 200, 160, 40, null, null, "", null));




        MyExcelUtil<ProductStatisticsDTO> myExcelUtil = new MyExcelUtil<ProductStatisticsDTO>();
        List<ProductStatisticsDTO> list = convert(dataList);
        System.out.println(JSON.toJSONString(list));
        list.sort(new Comparator<ProductStatisticsDTO>() {
            @Override
            public int compare(ProductStatisticsDTO o1, ProductStatisticsDTO o2) {
                return o2.getDate().compareTo(o1.getDate());
            }
        });
        myExcelUtil.exportData(list, "公司配置内容数据", null);

    }






    public static List<ProductStatisticsDTO> convert(List<StatisticsTileDTO> originalList) {
        // 创建嵌套Map结构：日期 → 应用 → 产品 → 版本 → ProductVersion
        Map<String, Map<String, Map<String, Map<String, ProductStatisticsDTO.ProductVersion>>>> hierarchy = new HashMap<>();

        // 填充hierarchy
        for (StatisticsTileDTO dto : originalList) {
            String date = dto.getDate();
            String appName = dto.getApp();
            String productName = dto.getProduct();
            String versionName = dto.getProductVersion();

            // 创建并填充ProductVersion
            ProductStatisticsDTO.ProductVersion pv = new ProductStatisticsDTO.ProductVersion();
            pv.setProductVersion(versionName);
            pv.setRequestCount(dto.getRequestCount());
            pv.setHasResultCount(dto.getHasResultCount());
            pv.setNoResultCount(dto.getNoResultCount());
            pv.setHasResultPercentage(dto.getHasResultPercentage());
            pv.setTrafficPercentage(dto.getTrafficPercentage()); // 注意可能的拼写错误，需与目标类一致

            // 更新嵌套Map
            hierarchy
                    .computeIfAbsent(date, k -> new HashMap<>())
                    .computeIfAbsent(appName, k -> new HashMap<>())
                    .computeIfAbsent(productName, k -> new HashMap<>())
                    .put(versionName, pv);
        }

        // 转换为层级结构的DTO列表
        List<ProductStatisticsDTO> result = new ArrayList<>();

        hierarchy.forEach((date, appMap) -> {
            ProductStatisticsDTO flowDTO = new ProductStatisticsDTO();
            flowDTO.setDate(date);
            List<ProductStatisticsDTO.App> appList = new ArrayList<>();

            appMap.forEach((appName, productMap) -> {
                ProductStatisticsDTO.App app = new ProductStatisticsDTO.App();
                app.setApp(appName);
                List<ProductStatisticsDTO.Product> productList = new ArrayList<>();

                productMap.forEach((productName, versionMap) -> {
                    ProductStatisticsDTO.Product product = new ProductStatisticsDTO.Product();
                    product.setProduct(productName);
                    product.setDetails(new ArrayList<>(versionMap.values()));
                    productList.add(product);
                });

                app.setDetails(productList);
                appList.add(app);
            });

            flowDTO.setDetails(appList);
            result.add(flowDTO);
        });

        return result;
    }

}
