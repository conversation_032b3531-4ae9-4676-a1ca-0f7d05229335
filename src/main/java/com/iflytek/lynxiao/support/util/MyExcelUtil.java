package com.iflytek.lynxiao.support.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.iflytek.lynxiao.support.util.excel.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;

import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Log4j2
public class MyExcelUtil<T> {

    /**
     * @param sourceData 导出数据源
     * @param sheetName  sheetName
     */
    public void exportData(List<T> sourceData, String sheetName, HttpServletResponse response) {
        if (CollectionUtils.isEmpty(sourceData)) {
            return;
        }
        // 将源数据平铺成List<Map<String, Object>>类型
        ExportEntity exportEntity = flattenObject(sourceData);
        // 获取要导出的列字段
        List<String> columnList = exportEntity.getExportParams().stream()
                .sorted(Comparator.comparing(ExportParam::getOrder))
                .map(ExportParam::getFieldName)
                .collect(Collectors.toList());
        // 获取导出数据集
        List<List<Object>> exportDataList = new ArrayList<>();
        for (Map<String, Object> objectMap : exportEntity.getData()) {
            List<Object> data = new ArrayList<>();
            columnList.forEach(columnName -> {
                data.add(objectMap.get(columnName));
            });
            exportDataList.add(data);
        }

        List<List<String>> headList = columnList.stream().map(Lists::newArrayList).collect(Collectors.toList());
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            WriteCellStyle[] cellStyles = cellStyleStrategy();

            /*EasyExcel.write("D:/baike/output.xlsx").sheet(sheetName)
                    .head(headList)
                    .registerWriteHandler(new MergeStrategy(exportEntity))
                    .registerWriteHandler(new HeadHandler(exportEntity))
                    .registerWriteHandler(new HorizontalCellStyleStrategy(cellStyles[0], cellStyles[1]))
                    .doWrite(exportDataList);*/
            EasyExcel.write(response.getOutputStream()).sheet(sheetName)
                    .head(headList)
                    .registerWriteHandler(new MergeStrategy(exportEntity))
                    .registerWriteHandler(new HeadHandler(exportEntity))
                    .registerWriteHandler(new HorizontalCellStyleStrategy(cellStyles[0], cellStyles[1]))
                    .doWrite(exportDataList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 获取导出数据：导出字段-值
     * 合并区间map：导出字段-[start, end]
     */
    public ExportEntity flattenObject(Collection<T> sourceData) {
        Map<String, List<Integer[]>> mergeMap = new HashMap<>();
        // 判断源数据class
        Class aClass = sourceData.stream().findFirst().get().getClass();
        // 获取导出字段
        List<ExportParam> exportParams = getAllExportField(aClass, "");
        Preconditions.checkArgument(!exportParams.isEmpty(), "export field not found !");
        // 获取导出数据：导出字段-值 合并区间map：导出字段-[start, end]
        List<Map<String, Object>> target = new ArrayList<>();
        int startRow = 0;
        for (Object sourceDataLine : sourceData) {
            List<Map<String, Object>> flatData = flattenObject(sourceDataLine, "", startRow, mergeMap);
            startRow += flatData.size();
            target.addAll(flatData);
        }
        // 包装为导出实体
        ExportEntity exportEntity = new ExportEntity();
        exportEntity.setExportParams(exportParams);
        exportEntity.setData(target);
        exportEntity.setMergeRowMap(mergeMap);
        return exportEntity;
    }

    /**
     * @param data     数据行
     * @param preNode  上一层级字段名
     * @param startRow 当前数据行所在行数
     * @param mergeMap 各字段合并行数组
     */
    private List<Map<String, Object>> flattenObject(Object data, String preNode, Integer startRow,
                                                    Map<String, List<Integer[]>> mergeMap) {
        List<Map<String, Object>> flatList = new ArrayList<>();
        if (null == data) {
            return flatList;
        }

        // 获取不为null的excel导出字段
        Class<?> aClass = data.getClass();
        List<Field> collectionFields = new ArrayList<>();
        List<Field> entityFields = new ArrayList<>();
        List<Field> propertyFields = new ArrayList<>();
        for (Field field : aClass.getDeclaredFields()) {
            Object value = getFieldValue(field, data);
            if (null == value) {
                continue;
            }
            if (isCollection(field)) {
                collectionFields.add(field);
            } else if (isEntity(field)) {
                entityFields.add(field);
            } else if (isProperty(field)) {
                propertyFields.add(field);
            }
        }

        // 对被@MyExcelCollection和@MyExcelEntity注解修饰的field继续flat
        List<Map<String, Object>> entityFlatData = flattenEntityFields(entityFields, data, preNode, startRow, mergeMap);
        List<List<Map<String, Object>>> collectionFlatData = flattenCollectionFields(collectionFields, data, preNode,
                startRow, mergeMap);

        Map<String, Object> objectMap = Collections.emptyMap();
        if (collectionFields.isEmpty() && entityFields.isEmpty()) {
            objectMap = flattenPropertyFields(propertyFields, data, preNode);
        }
        // 解决当前层级collecting和entity属性对应的值都是null时，导致property属性也被自动忽略，无法导出的问题
        if (collectionFlatData.isEmpty() && entityFlatData.isEmpty()) {
            objectMap = flattenPropertyFields(propertyFields, data, preNode);
        }
        if (!objectMap.isEmpty()) {
            flatList.add(objectMap);
        }

        // 当前层级所有平铺列表
        List<List<Map<String, Object>>> allFlatData = Lists.newArrayList();
        if (!entityFlatData.isEmpty()) {
            allFlatData.add(entityFlatData);
        }
        if (!collectionFlatData.isEmpty()) {
            allFlatData.addAll(collectionFlatData);
        }

        List<Map<String, Object>> mergeList = mergeList(data, allFlatData, propertyFields, preNode, startRow, mergeMap);
        if (!mergeList.isEmpty()) {
            flatList.addAll(mergeList);
        }

        return flatList;
    }

    /**
     * flat被@MyExcelEntity注解修饰的field的平铺结果和合并列表
     */
    private List<Map<String, Object>> flattenEntityFields(List<Field> entityFields, Object data, String preNode,
                                                          Integer startRow, Map<String, List<Integer[]>> mergeMap) {
        List<Map<String, Object>> entityFlatData = Lists.newArrayList();
        for (Field entityField : entityFields) {
            entityFlatData = flattenObject(getFieldValue(entityField, data),
                    buildPreNode(preNode, entityField.getName()), startRow, mergeMap);
        }
        return entityFlatData;
    }

    /**
     * flat被@MyExcelCcollection注解修饰的field的平铺结果和合并列表
     */
    private List<List<Map<String, Object>>> flattenCollectionFields(List<Field> collectionFields, Object data,
                                                                    String preNode, Integer startRow, Map<String, List<Integer[]>> mergeMap) {
        List<List<Map<String, Object>>> collectionFlatData = Lists.newArrayList();
        for (Field collectionField : collectionFields) {
            Collection collectionValue = (Collection) getFieldValue(collectionField, data);

            // 当前集合字段平铺而成的数据列表
            List<Map<String, Object>> collectionObjectValue = new ArrayList<>();
            // 间隔行数
            Integer row = 0;
            for (Object value : collectionValue) {
                List<Map<String, Object>> flatData = flattenObject(value,
                        buildPreNode(preNode, collectionField.getName()), startRow + row, mergeMap);
                if (!flatData.isEmpty()) {
                    collectionObjectValue.addAll(flatData);
                    // 下条数据的起始间隔行
                    row += flatData.size();
                }
            }
            if (!collectionObjectValue.isEmpty()) {
                collectionFlatData.add(collectionObjectValue);
            }
        }
        return collectionFlatData;
    }

    /**
     * flat被@MyExcelProperty注解修饰的field的平铺结果和合并列表
     */
    private Map<String, Object> flattenPropertyFields(List<Field> propertyFields, Object data, String preNode) {
        Map<String, Object> flatMap = new HashMap<>();
        for (Field field : propertyFields) {
            flatMap.put(buildPreNode(preNode, field.getName()), getFieldValue(field, data));
        }
        return flatMap;
    }

    /**
     * 返回待合并的集合 层级字段名：[start, end]
     */
    private List<Map<String, Object>> mergeList(Object data, List<List<Map<String, Object>>> allFlatData,
                                                List<Field> propertyFields, String preNode, Integer startRow, Map<String, List<Integer[]>> mergeMap) {
        List<Map<String, Object>> flatList = new ArrayList<>();

        // 当前层级下多个集合字段的最大长度即为当前层级其他字段的合并行数
        Integer maxSize = 0;
        for (List<Map<String, Object>> list : allFlatData) {
            maxSize = Math.max(maxSize, list.size());
        }

        // 记录集合同层级其他字段的合并行数
        if (maxSize > 0) {
            buildMerge(propertyFields, preNode, startRow, startRow + maxSize, mergeMap);
        }

        // 需要合并
        if (maxSize > 1) {
            // 重新构建平铺的list
            List<Map<String, Object>> mergeFlatData = new ArrayList<>(maxSize);
            for (int i = 0; i < maxSize; i++) {
                mergeFlatData.add(new HashMap<>());
            }

            for (List<Map<String, Object>> flatData : allFlatData) {
                for (int i = 0; i < flatData.size(); i++) {
                    Map<String, Object> flatMap = new HashMap<>(flatData.get(i));

                    // 添加同层级字段值
                    if (CollectionUtils.isNotEmpty(propertyFields)) {
                        for (Field field : propertyFields) {
                            flatMap.put(buildPreNode(preNode, field.getName()), getFieldValue(field, data));
                        }
                    }
                    mergeFlatData.get(i).putAll(flatMap);
                }
            }
            flatList.addAll(mergeFlatData);
        } else {
            // 解决只有单行数据时，前节点无法flat的问题
            Map<String, Object> result = new HashMap<>();
            for (List<Map<String, Object>> list : allFlatData) {
                for (Field propertyField : propertyFields) {
                    result.put(buildPreNode(preNode, propertyField.getName()), getFieldValue(propertyField, data));
                }
                result.putAll(list.get(0));
            }
            if (!result.isEmpty()) {
                flatList.add(result);
            }
        }
        return flatList;
    }

    /**
     * @param mergeFields   需要合并的字段
     * @param preNode       上一层级字段名
     * @param mergeStartRow 合并开始行
     * @param mergeEndRow   合并结束行
     * @param mergeMap      各字段合并行数组
     */
    private void buildMerge(List<Field> mergeFields, String preNode, Integer mergeStartRow, Integer mergeEndRow,
                            Map<String, List<Integer[]>> mergeMap) {
        for (Field mergeField : mergeFields) {
            String fieldName = buildPreNode(preNode, mergeField.getName());
            mergeMap.computeIfAbsent(fieldName, k1 -> new ArrayList<>());

            Integer[] rowInterval = new Integer[2];
            // 合并开始行
            rowInterval[0] = mergeStartRow;
            // 合并结束行
            rowInterval[1] = mergeEndRow;
            mergeMap.get(fieldName).add(rowInterval);
        }
    }

    /**
     * 获取导出参数
     *
     * @param clazz   导出类
     * @param preNode 上一层级字段名
     */
    private List<ExportParam> getAllExportField(Class<?> clazz, String preNode) {
        List<ExportParam> exportFields = new ArrayList<>();

        // 遍历类对象中声明的所有field
        for (Field declaredField : clazz.getDeclaredFields()) {
            // 检查field是否被注解
            MyExcelProperty myExcelProperty = declaredField.getDeclaredAnnotation(MyExcelProperty.class);
            MyExcelEntity myExcelEntity = declaredField.getDeclaredAnnotation(MyExcelEntity.class);
            MyExcelCollection myExcelCollection = declaredField.getDeclaredAnnotation(MyExcelCollection.class);
            // 构建层级关系，比如：lineConfigs.msgConfigs.msgName
            String fieldName = buildPreNode(preNode, declaredField.getName());
            // @MyExcelProperty注解的字段直接作为输出列
            if (null != myExcelProperty) {
                ExportParam exportParam = new ExportParam()
                        .setFieldName(fieldName)
                        .setColumnName(myExcelProperty.name())
                        .setOrder(myExcelProperty.index());
                exportFields.add(exportParam);
            }
            // @MyExcelEntity注解的字段继续遍历
            else if (null != myExcelEntity) {
                exportFields.addAll(getAllExportField(declaredField.getType(), fieldName));
            }
            // @MyExcelCollection注解的字段继续遍历
            else if (null != myExcelCollection) {
                boolean isCollection = Collection.class.isAssignableFrom(declaredField.getType());
                if (!isCollection) {
                    continue;
                }
                ParameterizedType pt = (ParameterizedType) declaredField.getGenericType();
                Class<?> clz = (Class<?>) pt.getActualTypeArguments()[0];
                exportFields.addAll(getAllExportField(clz, fieldName));
            }
        }
        return exportFields;
    }

    /**
     * 返回某个字段是否被@MyExccelProperty注解
     */
    private boolean isProperty(Field field) {
        return null != field.getDeclaredAnnotation(MyExcelProperty.class);
    }

    /**
     * 返回某个字段是否被@MyExccelEntity注解
     */
    private boolean isEntity(Field field) {
        return null != field.getDeclaredAnnotation(MyExcelEntity.class);
    }

    /**
     * 返回某个字段是否被@MyExccelCollection注解
     */
    private boolean isCollection(Field field) {
        boolean isCollection = Collection.class.isAssignableFrom(field.getType());
        return isCollection && null != field.getAnnotation(MyExcelCollection.class);
    }

    /**
     * @param preNode   上一层级字段名
     * @param fieldName 当前字段名
     */
    private String buildPreNode(String preNode, String fieldName) {
        StringBuffer sb = new StringBuffer();
        String DELIMITER = ".";
        return StringUtils.isEmpty(preNode) ? fieldName
                : sb.append(preNode).append(DELIMITER).append(fieldName).toString();
    }

    /**
     * 返回对象中某个字段的值
     */
    private Object getFieldValue(Field field, Object sourceObject) {
        Object fieldValue;
        try {
            // 允许访问private属性
            field.setAccessible(true);
            fieldValue = field.get(sourceObject);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        return fieldValue;
    }


    /**
     * 单元格样式
     */
    public WriteCellStyle[] cellStyleStrategy() {
        WriteCellStyle[] styles = new WriteCellStyle[2];

        WriteFont titleFont = new WriteFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setBold(true);

        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headCellStyle.setWriteFont(titleFont);
        styles[0] = headCellStyle;

        WriteCellStyle cellStyle = new WriteCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        styles[1] = cellStyle;

        return styles;
    }


    /**
     * 导出方法  支持动态列
     *
     * @param response            Http响应对象
     * @param fileName            下载文件名（不带后缀）
     * @param dataList            数据列表
     * @param fixedColumns        固定列配置
     * @param dynamicMapExtractor 动态列提取器
     */
    public static <T> void exportWithDynamicHeaders(HttpServletResponse response,
                                                    String fileName,
                                                    List<T> dataList,
                                                    LinkedHashMap<String, Function<T, Object>> fixedColumns,
                                                    Function<T, Map<String, Object>> dynamicMapExtractor) throws IOException {

        // 设置响应头
        configureResponse(response, fileName);

        try (Workbook workbook = new XSSFWorkbook();
             OutputStream out = response.getOutputStream()) {

            Sheet sheet = workbook.createSheet("Sheet1");

            // 创建表头样式（灰色背景+加粗）
            CellStyle headerStyle = createHeaderStyle(workbook);

            // 收集动态列头
            Set<String> dynamicHeaders = collectDynamicHeaders(dataList, dynamicMapExtractor);

            // 创建表头行
            createHeaderRow(sheet, headerStyle, fixedColumns, dynamicHeaders);

            // 填充数据
            fillData(sheet, dataList, fixedColumns, dynamicHeaders, dynamicMapExtractor, workbook);

            workbook.write(out);
        }
    }


    private static void configureResponse(HttpServletResponse response, String fileName) {
        response.reset();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + fileName + ".xlsx\"");
    }


    private static <T> Set<String> collectDynamicHeaders(List<T> dataList,
                                                         Function<T, Map<String, Object>> dynamicMapExtractor) {
        return dataList.stream()
                .map(dynamicMapExtractor)
                .flatMap(map -> {
                    if (map != null) {
                        return map.keySet().stream();
                    } else {
                        return Stream.empty(); // 如果 map 是 null，返回空流
                    }
                })
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    private static <T> void createHeaderRow(Sheet sheet, CellStyle headerStyle,
                                            LinkedHashMap<String, Function<T, Object>> fixedColumns,
                                            Set<String> dynamicHeaders) {
        Row headerRow = sheet.createRow(0);
        int colIdx = 0;

        // 固定列头
        for (String header : fixedColumns.keySet()) {
            Cell cell = headerRow.createCell(colIdx++);
            cell.setCellValue(header);
            cell.setCellStyle(headerStyle);
        }

        // 动态列头
        for (String header : dynamicHeaders) {
            Cell cell = headerRow.createCell(colIdx++);
            cell.setCellValue(header);
            cell.setCellStyle(headerStyle);
        }
    }

    private static <T> void fillData(Sheet sheet,
                                     List<T> dataList,
                                     LinkedHashMap<String, Function<T, Object>> fixedColumns,
                                     Set<String> dynamicHeaders,
                                     Function<T, Map<String, Object>> dynamicMapExtractor,
                                     Workbook workbook) {

        int rowIdx = 1;
        for (T item : dataList) {
            Row row = sheet.createRow(rowIdx++);
            fillRow(row, item, fixedColumns, dynamicHeaders, dynamicMapExtractor, workbook);
        }
    }

    private static <T> void fillRow(Row row,
                                    T item,
                                    LinkedHashMap<String, Function<T, Object>> fixedColumns,
                                    Set<String> dynamicHeaders,
                                    Function<T, Map<String, Object>> dynamicMapExtractor,
                                    Workbook workbook) {

        int colIdx = 0;
        Map<String, Object> dynamicValues = dynamicMapExtractor.apply(item);

        // 填充固定列
        for (Function<T, Object> extractor : fixedColumns.values()) {
            Object value = extractor.apply(item);
            setCellValue(row.createCell(colIdx++), value, workbook);
        }

        // 填充动态列
        for (String header : dynamicHeaders) {
            Object value = dynamicValues.getOrDefault(header, "");
            setCellValue(row.createCell(colIdx++), value, workbook);
        }
    }

    private static void setCellValue(Cell cell, Object value, Workbook workbook) {
        if (value == null) {
            cell.setCellValue("");
            return;
        }

        if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Date) {
            handleDateType(cell, (Date) value, workbook);
        } else if (value instanceof Calendar) {
            handleDateType(cell, ((Calendar) value).getTime(), workbook);
        } else {
            cell.setCellValue(value.toString());
        }
    }


    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置灰色背景
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置加粗字体
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        // 可选：设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }

    private static void handleDateType(Cell cell, Date date, Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(workbook.getCreationHelper()
                .createDataFormat()
                .getFormat("yyyy-MM-dd HH:mm:ss"));
        cell.setCellValue(date);
        cell.setCellStyle(style);
    }


}
