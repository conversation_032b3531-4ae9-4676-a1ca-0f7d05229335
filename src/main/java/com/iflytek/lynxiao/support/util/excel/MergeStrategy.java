package com.iflytek.lynxiao.support.util.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class MergeStrategy extends AbstractMergeStrategy {

    private final ExportEntity exportEntity;

    public MergeStrategy(ExportEntity exportEntity) {
        this.exportEntity = exportEntity;
    }


    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer integer) {

        //mergeMap需要加上head的行数
        String fieldName = head.getHeadNameList().get(0);
        List<Integer[]> mergeRow = exportEntity.getMergeRowMap().get(fieldName);
        if (CollectionUtils.isEmpty(mergeRow)) {
            return;
        }
        int currentColumnIndex = cell.getColumnIndex();
        int currentRowIndex = cell.getRowIndex();
        //表头所占行数
        int headHeight = head.getHeadNameList().size();
        for (Integer[] integers : mergeRow) {
            if (currentRowIndex == integers[1] + headHeight - 1) {
                // 解决单行数据导出时报错：merge region A2 must contains 2 or more cells
                if (integers[1] - integers[0] == 1) {
                    continue;
                }
                sheet.addMergedRegion(new CellRangeAddress(integers[0] + headHeight, integers[1] + headHeight - 1,
                        currentColumnIndex, currentColumnIndex));
            }
        }

    }
}
