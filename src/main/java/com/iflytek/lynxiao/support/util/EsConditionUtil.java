package com.iflytek.lynxiao.support.util;

import com.alibaba.fastjson2.JSON;
import com.iflytek.lynxiao.support.consts.StatisticsModule;
import com.iflytek.lynxiao.support.dto.MailStatisticsConditionDTO;
import com.iflytek.lynxiao.support.service.elastic.ElasticSearchService;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class EsConditionUtil {

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Value("${lynxiao.flow.analysis.health.scene.appIds}")
    private String healthSceneAppIds;

    public List<MailStatisticsConditionDTO> getFlowAnalysisCondition(String region, String bizCode, String module, String id, String field, String appId) {

        List<MailStatisticsConditionDTO> result = new ArrayList<>();

        // type
        MailStatisticsConditionDTO type = new MailStatisticsConditionDTO("type.keyword", "EQ", "SearchAPI-Response");
        result.add(type);
        // 业务应用概览
        if (StatisticsModule.APP.getCode().equals(module)) {
            MailStatisticsConditionDTO app = new MailStatisticsConditionDTO( "appId", "EQ", bizCode);
            result.add(app);
        }
        // 产品方案概览
        if (StatisticsModule.PRO_CODE.getCode().equals(module)) {
            MailStatisticsConditionDTO pvid = new MailStatisticsConditionDTO( "pvid", "EQ", bizCode);
            result.add(pvid);
            MailStatisticsConditionDTO appIdCondition = new MailStatisticsConditionDTO(  "appId", "EQ", id);
            result.add(appIdCondition);
        }
        // 医疗场景标签流量占比统计
        if (StatisticsModule.HEALTH.getCode().equals(module)) {
            MailStatisticsConditionDTO scene = new MailStatisticsConditionDTO( "scene", "CONT", bizCode);
            result.add(scene);
            MailStatisticsConditionDTO appIdCondition = new MailStatisticsConditionDTO( "appId", "IN", healthSceneAppIds);
            result.add(appIdCondition);
        }

        // 埋点
        if (StatisticsModule.BURIAL.getCode().equals(module)) {
            MailStatisticsConditionDTO fromType = new MailStatisticsConditionDTO( field, "CONT", bizCode);
            result.add(fromType);
            MailStatisticsConditionDTO pvid = new MailStatisticsConditionDTO( "pvid", "EQ", id);
            result.add(pvid);
            MailStatisticsConditionDTO appIdCondition = new MailStatisticsConditionDTO( "appId", "EQ", appId);
            result.add(appIdCondition);
        }

        return result;

    }

    private List<MailStatisticsConditionDTO> getHasResultFlowAnalysisCondition(String region, String bizCode, String module, String id, String field, String appId) {

        List<MailStatisticsConditionDTO> result = new ArrayList<>();
        // type
        MailStatisticsConditionDTO type = new MailStatisticsConditionDTO("type.keyword", "EQ", "SearchAPI-Response");
        result.add(type);
        // 业务应用概览
        if (StatisticsModule.APP.getCode().equals(module)) {
            MailStatisticsConditionDTO app = new MailStatisticsConditionDTO( "appId", "EQ", bizCode);
            result.add(app);
        }
        // 产品方案概览
        if (StatisticsModule.PRO_CODE.getCode().equals(module)) {
            MailStatisticsConditionDTO app = new MailStatisticsConditionDTO( "pvid", "EQ", bizCode);
            result.add(app);
            MailStatisticsConditionDTO appIdCondition = new MailStatisticsConditionDTO( "appId", "EQ", id);
            result.add(appIdCondition);
        }
        // 医疗场景标签流量占比统计
        if (StatisticsModule.HEALTH.getCode().equals(module)) {
            MailStatisticsConditionDTO scene = new MailStatisticsConditionDTO( "scene", "CONT", bizCode);
            result.add(scene);
            MailStatisticsConditionDTO appIdCondition = new MailStatisticsConditionDTO(  "appId", "IN", healthSceneAppIds);
            result.add(appIdCondition);
        }

        // 埋点
        if (StatisticsModule.BURIAL.getCode().equals(module)) {

            MailStatisticsConditionDTO fromType = new MailStatisticsConditionDTO( field, "CONT", bizCode);
            result.add(fromType);
            MailStatisticsConditionDTO pvid = new MailStatisticsConditionDTO( "pvid", "EQ", id);
            result.add(pvid);
            MailStatisticsConditionDTO appIdCondition = new MailStatisticsConditionDTO( "appId", "EQ", appId);
            result.add(appIdCondition);
        }

        // docCount > 0
        MailStatisticsConditionDTO hasResult = new MailStatisticsConditionDTO( "docsCount", "GT", "0");
        result.add(hasResult);
        return result;

    }

    private List<MailStatisticsConditionDTO> getSuccessFlowAnalysisCondition(String region, String bizCode, String module, String appId) {

        List<MailStatisticsConditionDTO> result = new ArrayList<>();
        // type
        MailStatisticsConditionDTO type = new MailStatisticsConditionDTO("type.keyword", "EQ", "SearchAPI-Response");
        result.add(type);

        // 产品方案概览
        if (StatisticsModule.PRO_CODE.getCode().equals(module)) {
            MailStatisticsConditionDTO app = new MailStatisticsConditionDTO("pvid", "EQ", bizCode);
            result.add(app);
            MailStatisticsConditionDTO appIdCondition = new MailStatisticsConditionDTO("appId", "EQ", appId);
            result.add(appIdCondition);
        }

        // code=0
        MailStatisticsConditionDTO hasResult = new MailStatisticsConditionDTO("code", "EQ", "0");
        result.add(hasResult);
        return result;

    }

    // 封装查询请求总量的逻辑
    public long queryTotalNum(String region, String indexName, String module, String bizCode, String id, String filed, String model) {
        List<MailStatisticsConditionDTO> flowAnalysisCondition = getFlowAnalysisCondition(region, bizCode, module, id, filed, model);
        List<QueryBuilder> queryBuilders = elasticSearchService.recursionCondition(JSON.toJSONString(flowAnalysisCondition));
        return elasticSearchService.countDocuments(region, indexName, queryBuilders);

    }

    // 封装查询有结果量的逻辑
    public long queryHasResultNum(String region, String indexName, String module, String bizCode, String id, String filed, String model) {
        List<MailStatisticsConditionDTO> flowAnalysisCondition = getHasResultFlowAnalysisCondition(region, bizCode, module, id, filed, model);
        List<QueryBuilder> queryBuilders = elasticSearchService.recursionCondition(JSON.toJSONString(flowAnalysisCondition));
        return elasticSearchService.countDocuments(region, indexName, queryBuilders);
    }


    // 封装查询有结果量的逻辑
    public long querySuccessNum(String region, String indexName, String module, String bizCode, String appId) {
        List<MailStatisticsConditionDTO> flowAnalysisCondition = getSuccessFlowAnalysisCondition(region, bizCode, module, appId);
        List<QueryBuilder> queryBuilders = elasticSearchService.recursionCondition(JSON.toJSONString(flowAnalysisCondition));
        return elasticSearchService.countDocuments(region, indexName, queryBuilders);
    }

}
