package com.iflytek.lynxiao.support.util;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Log4j2
public class DateUtil {

    /**
     * 获取索引名称， 如果传入的日期为空，则默认返回昨天的索引名称
     *
     * @param paramDate 例如： "2023-10-01"
     * @return 索引名称，例如： "lynxiao_flow-2023.10.01"
     */
    public static String getIndexName(String paramDate) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy.MM.dd");
        try {
            String indexPre = "lynxiao_flow-";
            if (StringUtils.isBlank(paramDate)) {
                // 如果paramDate为空，则获取前一天的日期
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, -1);
                return indexPre + outputFormat.format(calendar.getTime());
            }
            Date date = inputFormat.parse(paramDate);
            return indexPre + outputFormat.format(date);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("获取索引名称失败, 请检查日期格式是否正确", e);
        }
    }

    /**
     * 返回格式化的date  yyyy-MM-dd
     * 传了date则将输出yyyy-MM-dd格式；
     * 未传返回昨天
     *
     * @param date 例如： "2023-10-01"
     */
    public static String getDate(String date) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            if (StringUtils.isBlank(date)) {
                // 如果paramDate为空，则获取前一天的日期
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, -1);
                return inputFormat.format(calendar.getTime());
            } else {
                // 如果传入了日期，则直接返回该日期
                Date parsedDate = inputFormat.parse(date);
                return inputFormat.format(parsedDate);
            }
        } catch (Exception e) {
            throw new RuntimeException("获取日期失败, 请检查日期格式是否正确", e);
        }
    }

    public static long getBetweenDateCount(String beginDate, String endDate) {
        // 将字符串转换为LocalDate对象
        LocalDate date1 = LocalDate.parse(beginDate);
        LocalDate date2 = LocalDate.parse(endDate);
        // 计算两个日期之间的天数差
        return ChronoUnit.DAYS.between(date1, date2.plusDays(1));
    }


    // 判断两个日期是否超过一年
    public static boolean isExactlyOneYearApart(String beginDate, String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(beginDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);

        // 计算两个日期之间的天数差异
        long daysBetween = ChronoUnit.DAYS.between(start, end);

        // 一年的天数通常为365天，闰年为366天
        // 如果开始日期和结束日期之间恰好相差365或366天，则认为它们之间恰好相差一年
        return daysBetween == 365 || daysBetween == 366;
    }


    // 计算环比
    public static String[] getPreviousPeriod(String beginDate, String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(beginDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);

        // 计算开始日期和结束日期之间的天数差异
        long daysBetween = ChronoUnit.DAYS.between(start, end);

        // 计算前一个周期的结束日期（当前周期的开始日期减去一天）
        LocalDate previousPeriodEnd = start.minusDays(1);
        // 计算前一个周期的开始日期（结束日期减去之前计算的天数差异）
        LocalDate previousPeriodStart = previousPeriodEnd.minusDays(daysBetween);

        // 将LocalDate对象格式化为字符串
        String previousPeriodStartDate = previousPeriodStart.format(formatter);
        String previousPeriodEndDate = previousPeriodEnd.format(formatter);

        // 返回前一个周期的开始和结束日期
        System.out.println(Arrays.toString(new String[]{previousPeriodStartDate, previousPeriodEndDate}));
        return new String[]{previousPeriodStartDate, previousPeriodEndDate};
    }


    /**
     * 根据开始时间结束时间得到时间列表
     *
     * @param startTime yyyy-MM-dd
     * @param endTime   yyyy-MM-dd
     * @return List<String>
     */
    public static List<String> getDaysBetween(String startTime, String endTime) {
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
            return new ArrayList<>();
        }
        List<String> days = new ArrayList<>();

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startTime);
            Date end = sdf.parse(endTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(start);

            while (!calendar.getTime().after(end)) {
                days.add(sdf.format(calendar.getTime()));
                calendar.add(Calendar.DATE, 1);
            }
            days.sort(new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    if (StringUtils.isBlank(o1) || StringUtils.isBlank(o2)) {
                        return 0;
                    }
                    return o2.compareTo(o1);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
        return days;
    }

    public static String getSubjectDate(String date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM月dd日", Locale.getDefault());
        try {
            if (StringUtils.isBlank(date)) {
                // 如果paramDate为空，则获取前一天的日期
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, -1);
                return dateFormat.format(calendar.getTime());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;
    }


    public static void main(String[] args) {
        getPreviousPeriod("2025-03-01", "2025-03-31");
    }
}
