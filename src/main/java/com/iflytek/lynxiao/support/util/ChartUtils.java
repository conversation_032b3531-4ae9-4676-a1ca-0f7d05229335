package com.iflytek.lynxiao.support.util;

import org.jfree.chart.ChartFactory;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.title.LegendTitle;
import org.jfree.data.category.DefaultCategoryDataset;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class ChartUtils {

    public static File generateLineChart(String title, DefaultCategoryDataset dataset) throws IOException {
        // 创建折线图
        JFreeChart chart = ChartFactory.createLineChart(
                title,
                "",
                "",
                dataset,
                PlotOrientation.VERTICAL,
                true,
                true,
                false
        );

        // 设置字体
        Font font = new Font("Noto Sans Mono CJK TC Bold:style=Bold,Regular", Font.BOLD, 12);
        chart.getTitle().setFont(font); // 设置标题字体
        chart.getCategoryPlot().getDomainAxis().setLabelFont(font); // 设置X轴标签字体
        chart.getCategoryPlot().getRangeAxis().setLabelFont(font); // 设置Y轴标签字体

        // 设置图例字体
        chart.getLegend().setItemFont(font);

        // 自定义图例
        LegendTitle legend = chart.getLegend();
        legend.setItemFont(font); // 设置图例字体


        // 创建文件保存路径
        File chartDir = new File("charts");
        if (!chartDir.exists()) {
            chartDir.mkdirs();
        }

        // 将图表保存为图片文件
        File chartFile = new File(chartDir, title + ".png");
        saveChartAsPNG(chartFile, chart, 800, 600);
        return chartFile;

    }


    public static void saveChartAsPNG(File file, JFreeChart chart, int width, int height) throws IOException {
        // 创建图表的图像
        BufferedImage bufferedImage = chart.createBufferedImage(width, height);
        // 将图像保存为 PNG 文件
        ImageIO.write(bufferedImage, "PNG", file);
    }



    public static String getHtmlStyle() {

        return "<!DOCTYPE html> <html lang=\"en\"> <head> <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"> <title>自研SS每日流量数据</title>" +
//                    "                        <body>" +
                "                        <style>" +
                "                             body {" +
                "                                   font-family: Arial, sans-serif;" +
                "                                   font-size: 14px; " +
                "                                   color: #333;" +
                "                                   }" +
                "                            table {" +
                "                                border-collapse: collapse; /* 合并边框 */" +
//                    "                                width: 100%;" +
                "                                background-color: #fff; /* 设置表格背景颜色为白色 */" +
                "                                color: #333; /* 设置字体颜色为黑色 */" +
                "                                font-size: 14px; /* 设置字号为14px */" +
                "                                border: 1px solid #ccc; /* 设置1像素宽度的灰色边框 */" +
                "                            }" +
                "                            th, td {" +
                "                                padding: 10px; /* 设置单元格内边距为8像素 */" +
                "                                border: 1px solid #ccc; /* 单元格边框 */" +
                "                            }" +
                "                            td {" +
                "                                 text-align: right; " +
                "                            }" +
                "                            th {" +
                "                                background-color: #4F94CD; /* 设置表头背景颜色 */" +
                "                                font-weight: bold; /* 设置表头字体为加粗 */" +
                "                                color: #FFFFFF;" +
                "                            }" +
                "                            caption {" +
//                    "                                color: #000;" +
                "                                /*font: italic 85%/1 arial,sans-serif;*/" +
                "                                font-size: 18px; /* 设置字号为14px */" +
                "                                padding: 1em 0;" +
                "                                text-align: left;" +
                "                            }" +
                "                            p {" +
                "                                color: #000;" +
                "                                /*font: italic 85%/1 arial,sans-serif;*/" +
                "                               font-size: 18px; /* 设置字号为14px */" +
                "                                padding: 1px 0;" +
                "                                text-align: left;" +
                "                            }" +
                "                            span {" +
                "                                font-weight: bold;" +
                "                                font-size: 20px; /* 设置字号为14px */" +
                "                            }" +
                "                        </style>" +
                "                       </head>" +
                "                       <body>" +
                "                        <p>" +
                "                           各位好！凌霄平台(Lynxiao)" + DateUtil.getSubjectDate(null) + "请求访问统计报表，如下：" +
                "                        </p>";

    }

}
