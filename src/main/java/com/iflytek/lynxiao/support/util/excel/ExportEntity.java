package com.iflytek.lynxiao.support.util.excel;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 导出实体类
 */
@Data
public class ExportEntity {
    /**
     * 导出参数
     */
    private List<ExportParam> exportParams;

    /**
     * 平铺后的数据
     */
    private List<Map<String,Object>> data;

    /**
     * 每列的合并行数组
     */
    private Map<String, List<Integer[]>> mergeRowMap;

}
