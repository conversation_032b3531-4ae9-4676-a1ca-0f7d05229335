package com.iflytek.lynxiao.support.util;

import skynet.boot.mysql.domain.AbstractAuditingEntity;

import java.time.Instant;

/**
 * 数据库实体工具类
 *
 * <AUTHOR>  2024/10/11 11:58
 */
public class AuditingEntityUtil {

    /**
     * 设置创建实体类通用参数：id、时间戳以及用户信息
     *
     * @param auditingEntity 实体类基类
     */
    public static void fillCreateValue(AbstractAuditingEntity<Long> auditingEntity) {
        // 设置雪花算法id
        Instant now = Instant.now();
        auditingEntity.setCreatedDate(now);
        auditingEntity.setLastModifiedDate(now);
    }

    /**
     * 设置更新实体类通用参数，时间戳以及用户信息
     *
     * @param auditingEntity 实体类基类
     */
    public static void fillUpdateValue(AbstractAuditingEntity<Long> auditingEntity) {
        auditingEntity.setLastModifiedDate(Instant.now());
    }
}
