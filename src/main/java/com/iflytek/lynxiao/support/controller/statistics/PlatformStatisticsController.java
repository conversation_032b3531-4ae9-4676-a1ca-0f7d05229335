package com.iflytek.lynxiao.support.controller.statistics;


import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.support.consts.BurialStatisticsModel;
import com.iflytek.lynxiao.support.consts.RegionCode;
import com.iflytek.lynxiao.support.dto.common.GeneratedMetaDict;
import com.iflytek.lynxiao.support.dto.statistics.*;
import com.iflytek.lynxiao.support.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.support.feign.region.MetaDictFeign;
import com.iflytek.lynxiao.support.service.statistics.PlatformStatisticsService;
import com.iflytek.lynxiao.support.util.MyExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "平台流量统计")
@RestController
@RequestMapping("/api/v1/statistics/")
@EnableSkynetSwagger2
public class PlatformStatisticsController {

    @Autowired
    private PlatformStatisticsService platformStatisticsService;
    @Resource
    private LynxiaoFeignClientManager lynxiaoFeignClientManager;


    @Operation(summary = "业务应用流量统计")
    @GetMapping("app/overview")
    public ApiResponse statisticsAppFlowAnalysis(@RequestParam(value = "beginDate") String beginDate,
                                                 @RequestParam(value = "endDate") String endDate,
                                                 @RequestParam(value = "appIds", required = false) String appIds,
                                                 @RequestParam(value = "regionCodes", required = false) String regions) {
        return new ApiResponse(JSONObject.of("data", platformStatisticsService.getAppTraffic(beginDate, endDate, appIds, regions)));
    }

    @Operation(summary = "环境流量分析")
    @GetMapping("evn/overview")
    public ApiResponse statisticsEnvFlowAnalysis(@RequestParam(value = "beginDate") String beginDate,
                                                 @RequestParam(value = "endDate") String endDate,
                                                 @RequestParam(value = "appIds", required = false) String appIds,
                                                 @RequestParam(value = "regionCodes", required = false) String regions,
                                                 @RequestParam(value = "productIds", required = false) String productIds) {
        return new ApiResponse(JSONObject.of("data", platformStatisticsService.getEnvTraffic(beginDate, endDate, appIds, regions, productIds)));
    }

    @Operation(summary = "埋点流量分析")
    @GetMapping("burial/overview")
    public ApiResponse statisticsBurialFlowAnalysis(@RequestParam(value = "beginDate") String beginDate,
                                                    @RequestParam(value = "endDate") String endDate,
                                                    @RequestParam(value = "appIds", required = false) String appIds,
                                                    @RequestParam(value = "regionCodes", required = false) String regions,
                                                    @RequestParam(value = "productIds", required = false) String productIds) {
        return new ApiResponse(JSONObject.of("data", platformStatisticsService.getBurialTraffic(beginDate, endDate, appIds, regions, productIds)));
    }


    @Operation(summary = "导出-业务应用流量汇总表")
    @GetMapping("export/app-summary")
    public void exportAppSummary(@RequestParam(value = "beginDate") String beginDate,
                                 @RequestParam(value = "endDate") String endDate,
                                 @RequestParam(value = "appIds", required = false) String appIds,
                                 @RequestParam(value = "regionCodes", required = false) String regions,
                                 HttpServletResponse response) {
        List<AppStatisticsSummaryDTO> appStatisticsSummary = platformStatisticsService.exportAppSummaryTraffic(beginDate, endDate, appIds, regions);
        MyExcelUtil<AppStatisticsSummaryDTO> excel = new MyExcelUtil<AppStatisticsSummaryDTO>();
        excel.exportData(appStatisticsSummary, "业务应用流量汇总表", response);

    }

    @Operation(summary = "查询-业务应用流量汇总表")
    @GetMapping("query/app-summary")
    public ApiResponse queryAppSummary(@RequestParam(value = "beginDate") String beginDate,
                                       @RequestParam(value = "endDate") String endDate,
                                       @RequestParam(value = "appIds", required = false) String appIds,
                                       @RequestParam(value = "regionCodes", required = false) String regions) {
        List<AppStatisticsSummaryDTO> appStatisticsSummary = platformStatisticsService.exportAppSummaryTraffic(beginDate, endDate, appIds, regions);
        return new ApiResponse(JSONObject.of("data", appStatisticsSummary));
    }

    @Operation(summary = "导出-业务应用流量分析日报表")
    @GetMapping("export/app-day")
    public void exportAppDay(@RequestParam(value = "beginDate") String beginDate,
                             @RequestParam(value = "endDate") String endDate,
                             @RequestParam(value = "appIds", required = false) String appIds,
                             @RequestParam(value = "regionCodes", required = false) String regions,
                             HttpServletResponse response) {
        List<AppStatisticsDayDTO> appStatisticsDay = platformStatisticsService.exportAppDayTraffic(beginDate, endDate, appIds, regions);
        MyExcelUtil<AppStatisticsDayDTO> excel = new MyExcelUtil<AppStatisticsDayDTO>();
        excel.exportData(appStatisticsDay, "业务应用流量分析日报表", response);

    }

    @Operation(summary = "查询-业务应用流量分析日报表")
    @GetMapping("query/app-day")
    public ApiResponse queryAppDay(@RequestParam(value = "beginDate") String beginDate,
                                   @RequestParam(value = "endDate") String endDate,
                                   @RequestParam(value = "appIds", required = false) String appIds,
                                   @RequestParam(value = "regionCodes", required = false) String regions) {
        List<AppStatisticsDayDTO> appStatisticsDay = platformStatisticsService.exportAppDayTraffic(beginDate, endDate, appIds, regions);
        return new ApiResponse(JSONObject.of("data", appStatisticsDay));
    }

    @Operation(summary = "导出-按环境日期段统计业务应用流量明细")
    @GetMapping("export/app-day-env")
    public void exportAppDayEnv(@RequestParam(value = "beginDate") String beginDate,
                                @RequestParam(value = "endDate") String endDate,
                                @RequestParam(value = "appIds", required = false) String appIds,
                                @RequestParam(value = "regionCodes", required = false) String regions,
                                HttpServletResponse response) {
        List<AppStatisticsDayEnvDTO> appStatisticsDay = platformStatisticsService.exportAppDayEnvTraffic(beginDate, endDate, appIds, regions);
        MyExcelUtil<AppStatisticsDayEnvDTO> excel = new MyExcelUtil<AppStatisticsDayEnvDTO>();
        excel.exportData(appStatisticsDay, "按环境日期段统计业务应用流量明细", response);

    }

    @Operation(summary = "查询-按环境日期段统计业务应用流量明细")
    @GetMapping("query/app-day-env")
    public ApiResponse queryAppDayEnv(@RequestParam(value = "beginDate") String beginDate,
                                      @RequestParam(value = "endDate") String endDate,
                                      @RequestParam(value = "appIds", required = false) String appIds,
                                      @RequestParam(value = "regionCodes", required = false) String regions) {
        List<AppStatisticsDayEnvDTO> appStatisticsDay = platformStatisticsService.exportAppDayEnvTraffic(beginDate, endDate, appIds, regions);
        return new ApiResponse(JSONObject.of("data", appStatisticsDay));
    }

    @Operation(summary = "导出-按环境日期段统计产品方案流量明细")
    @GetMapping("export/product-day-env")
    public void exportProductDayEnv(@RequestParam(value = "beginDate") String beginDate,
                                    @RequestParam(value = "endDate") String endDate,
                                    @RequestParam(value = "appIds", required = false) String appIds,
                                    @RequestParam(value = "regionCodes", required = false) String regions,
                                    HttpServletResponse response) {
        List<ProductEnvStatisticsDTO> appStatisticsDay = platformStatisticsService.exportProductDayEnvTraffic(beginDate, endDate, appIds, regions);
        MyExcelUtil<ProductEnvStatisticsDTO> excel = new MyExcelUtil<ProductEnvStatisticsDTO>();
        excel.exportData(appStatisticsDay, "按环境日期段统计业务应用流量明细", response);

    }

    @Operation(summary = "查询-按环境日期段统计产品方案流量明细")
    @GetMapping("query/product-day-env")
    public ApiResponse queryProductDayEnv(@RequestParam(value = "beginDate") String beginDate,
                                          @RequestParam(value = "endDate") String endDate,
                                          @RequestParam(value = "appIds", required = false) String appIds,
                                          @RequestParam(value = "regionCodes", required = false) String regions) {
        List<ProductEnvStatisticsDTO> appStatisticsDay = platformStatisticsService.exportProductDayEnvTraffic(beginDate, endDate, appIds, regions);
        return new ApiResponse(JSONObject.of("data", appStatisticsDay));
    }


    @Operation(summary = "导出-产品方案流量分析日报表")
    @GetMapping("export/product-day")
    public void exportProductDay(@RequestParam(value = "beginDate") String beginDate,
                                 @RequestParam(value = "endDate") String endDate,
                                 @RequestParam(value = "appIds", required = false) String appIds,
                                 @RequestParam(value = "regionCodes", required = false) String regions,
                                 HttpServletResponse response) {
        List<ProductStatisticsDTO> appStatisticsDay = platformStatisticsService.exportProductDayTraffic(beginDate, endDate, appIds, regions);
        MyExcelUtil<ProductStatisticsDTO> excel = new MyExcelUtil<ProductStatisticsDTO>();
        excel.exportData(appStatisticsDay, "按环境日期段统计业务应用流量明细", response);

    }

    @Operation(summary = "查询-产品方案流量分析日报表")
    @GetMapping("query/product-day")
    public ApiResponse queryProductDay(@RequestParam(value = "beginDate") String beginDate,
                                       @RequestParam(value = "endDate") String endDate,
                                       @RequestParam(value = "appIds", required = false) String appIds,
                                       @RequestParam(value = "regionCodes", required = false) String regions) {
        List<ProductStatisticsDTO> appStatisticsDay = platformStatisticsService.exportProductDayTraffic(beginDate, endDate, appIds, regions);
        return new ApiResponse(JSONObject.of("data", appStatisticsDay));
    }

    @Operation(summary = "获取埋点类型列表")
    @GetMapping("query/burialType")
    public ApiResponse burialType() {
        List<GeneratedMetaDict> appStatisticsDay = platformStatisticsService.getBurialType();
        return new ApiResponse(JSONObject.of("data", appStatisticsDay));
    }

    @Operation(summary = "获取埋点组合类型列表")
    @GetMapping("query/dynamics-burial-type")
    public ApiResponse dynamicsBurialType() {
        List<GeneratedMetaDict> dicts = platformStatisticsService.dynamicsBurialType();
        return new ApiResponse(JSONObject.of("data", dicts));
    }


    @Operation(summary = "导出-埋点分析")
    @GetMapping("export/{burialType}")
    public void exportBurial(@PathVariable String burialType,
                             @RequestParam(value = "beginDate") String beginDate,
                             @RequestParam(value = "endDate") String endDate,
                             @RequestParam(value = "appIds", required = false) String appIds,
                             @RequestParam(value = "regionCodes", required = false) String regions,
                             HttpServletResponse response) {
        List<BurialDTO> appStatisticsDay = platformStatisticsService.exportBurialTraffic(burialType, beginDate, endDate, appIds, regions);
        MyExcelUtil<BurialDTO> excel = new MyExcelUtil<BurialDTO>();
        excel.exportData(appStatisticsDay, "埋点分析流量明细", response);

    }

    @Operation(summary = "查询-埋点分析")
    @GetMapping("query/{burialType}")
    public ApiResponse queryBurial(@PathVariable String burialType,
                                   @RequestParam(value = "beginDate") String beginDate,
                                   @RequestParam(value = "endDate") String endDate,
                                   @RequestParam(value = "appIds", required = false) String appIds,
                                   @RequestParam(value = "regionCodes", required = false) String regions) {
        List<BurialDTO> appStatisticsDay = platformStatisticsService.exportBurialTraffic(burialType, beginDate, endDate, appIds, regions);
        return new ApiResponse(JSONObject.of("data", appStatisticsDay));
    }

    @Operation(summary = "查询-埋点组合分析")
    @GetMapping("query/dynamics/{burialType}")
    public ApiResponse queryDynamicsBurial(@PathVariable String burialType,
                                           @RequestParam(value = "beginDate") String beginDate,
                                           @RequestParam(value = "endDate") String endDate,
                                           @RequestParam(value = "appIds", required = false) String appIds,
                                           @RequestParam(value = "regionCodes", required = false) String regions) {
        List<BurialDTO> appStatisticsDay = platformStatisticsService.exportDynamicsBurialTraffic(burialType, beginDate, endDate, appIds, regions);
        return new ApiResponse(JSONObject.of("data", appStatisticsDay));
    }


    @Operation(summary = "导出-埋点分析")
    @GetMapping("export/dynamics/{burialType}")
    public void exportDyBurial(@PathVariable String burialType,
                               @RequestParam(value = "beginDate") String beginDate,
                               @RequestParam(value = "endDate") String endDate,
                               @RequestParam(value = "appIds", required = false) String appIds,
                               @RequestParam(value = "regionCodes", required = false) String regions,
                               HttpServletResponse response) {
        List<StatisticsTileDTO> burialTraffics = platformStatisticsService.exportTileDynamicsBurialTraffic(burialType, beginDate, endDate, appIds, regions);

        LinkedHashMap<String, Function<StatisticsTileDTO, Object>> fixedColumns = new LinkedHashMap<>();
        fixedColumns.put("日期", StatisticsTileDTO::getDate);
        fixedColumns.put("产品方案", StatisticsTileDTO::getProduct);
        fixedColumns.put("请求总数", StatisticsTileDTO::getRequestCount);
        fixedColumns.put("有结果数", StatisticsTileDTO::getHasResultCount);
        fixedColumns.put("有结果率", StatisticsTileDTO::getHasResultPercentage);
        fixedColumns.put("流量占比", StatisticsTileDTO::getTrafficPercentage);

        // 转换埋点名称
        MetaDictFeign metaDictFeign = lynxiaoFeignClientManager.buildWithRegion(MetaDictFeign.class, RegionCode.HF);
        List<GeneratedMetaDict> burialDicts = new ArrayList<>();
        Arrays.asList(BurialStatisticsModel.ONE.getCode(), BurialStatisticsModel.TWO.getCode(), BurialStatisticsModel.THREE.getCode()).forEach(t -> {
            ApiResponse apiResponse = metaDictFeign.getListByCode(t);
            List<GeneratedMetaDict> itemList = apiResponse.getPayload().getList("data", GeneratedMetaDict.class);
            burialDicts.addAll(itemList);
        });
        for (StatisticsTileDTO burialTraffic : burialTraffics) {
            Map<String, Object> convertMap = new HashMap<>();
            burialTraffic.getBurialItemMap().forEach((key, value) -> {
                String convertKey = burialDicts.stream()
                        .filter(dict -> dict.getCode().equals(key))
                        .map(GeneratedMetaDict::getName)
                        .findFirst()
                        .orElse(key);
                convertMap.put(convertKey, value);
            });
            burialTraffic.setBurialItemMap(convertMap);
        }
        try {
            MyExcelUtil.exportWithDynamicHeaders(response, "burialType_" + System.currentTimeMillis(), burialTraffics, fixedColumns, StatisticsTileDTO::getBurialItemMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


    }


}
