package com.iflytek.lynxiao.support.controller.portal;


import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.support.service.portal.PortalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "中心服务数据查询")
@RestController
@RequestMapping("/api/v1/portal/")
@EnableSkynetSwagger2
public class PortalController {

    @Autowired
    private PortalService portalService;

    @Operation(summary = "获取已经启用的业务应用")
    @GetMapping("enable-app")
    public ApiResponse getEnableApp() {
        return new ApiResponse(JSONObject.of("data", portalService.getEnableAppList()));
    }


    //todo 是否可能遗漏
    @Operation(summary = "查询所有已经上线启用的产品方案列表")
    @GetMapping("online-enabled-product-list")
    public ApiResponse findOnlineList() {
        return new ApiResponse(JSONObject.of("data", portalService.getOnlineProductList()));
    }
}
