package com.iflytek.lynxiao.support.controller.back;


import com.iflytek.lynxiao.support.service.statistics.CalculateService;
import com.iflytek.lynxiao.support.service.statistics.MailStatisticsService;
import com.iflytek.lynxiao.support.util.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;
import skynet.boot.pandora.api.ApiResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "辅助工具")
@RestController
@RequestMapping("/api/v1/back/")
@EnableSkynetSwagger2
public class BackController {

    @Autowired
    private MailStatisticsService mailStatisticsService;

    @Autowired
    private CalculateService calculateService;

    /**
     * 报表邮件
     */
    @Operation(summary = "发送流量分析邮件")
    @GetMapping("sendFlowAnalysisMailAll")
    public ApiResponse sendMail(@RequestParam(value = "beginDate", required = false) String begin,
                                @RequestParam(value = "endDate", required = false) String endDate,
                                @RequestParam(value = "to") String to,
                                @RequestParam(value = "cc", required = false) String cc,
                                @RequestParam(value = "type") String type) {
        mailStatisticsService.sendFlowAnalysisMail(begin, endDate, to, cc, type);
        return new ApiResponse();
    }

    /**
     * 业务应用流量统计
     *
     * @param date yyyy-MM-dd
     */
    @Operation(summary = "业务应用流量统计")
    @GetMapping("statisticsAppFlowAnalysis")
    public ApiResponse statisticsAppFlowAnalysis(@RequestParam(value = "date", required = false) String date) {
        calculateService.statisticsAppTraffic(date);
        return new ApiResponse();
    }

    @Operation(summary = "埋点流量统计")
    @GetMapping("statisticsBurialFlowAnalysis")
    public ApiResponse statisticsBurialFlowAnalysis(@RequestParam(value = "date", required = false) String date) {
        calculateService.statisticsBurialTraffic(date);
        return new ApiResponse();
    }

    @Operation(summary = "动态埋点流量统计")
    @GetMapping("statisticsDynamicsBurial")
    public ApiResponse refreshStatistics2(@RequestParam(value = "date") String date) {
        calculateService.statisticsDynamicsBurial(date);
        return new ApiResponse();
    }

    @Operation(summary = "业务唯一id流量统计")
    @GetMapping("statisticsBizEqualId")
    public ApiResponse refreshBizEqualId(@RequestParam(value = "date") String date) {
        calculateService.statisticsBizEqualId(date);
        return new ApiResponse();
    }

    /**
     * 刷新流量统计  todo 增加动态埋点
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    @Operation(summary = "刷新流量统计")
    @GetMapping("refreshStatistics")
    public ApiResponse refreshStatistics(@RequestParam(value = "beginDate") String beginDate,
                                         @RequestParam(value = "endDate") String endDate) {

        List<String> daysBetween = DateUtil.getDaysBetween(beginDate, endDate);
        for (String day : daysBetween) {
            calculateService.statisticsBurialTraffic(day);
            calculateService.statisticsAppTraffic(day);
        }
        return new ApiResponse();
    }

    @Operation(summary = "刷新埋点流量统计")
    @GetMapping("refreshBurialStatistics")
    public ApiResponse refreshBurialStatistics(@RequestParam(value = "beginDate") String beginDate,
                                               @RequestParam(value = "endDate") String endDate) {

        List<String> daysBetween = DateUtil.getDaysBetween(beginDate, endDate);
        for (String day : daysBetween) {
            calculateService.statisticsBurialTraffic(day);
        }
        return new ApiResponse();
    }
}
