package com.iflytek.lynxiao.support.controller.export;


import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.support.dto.export.ExportDTO;
import com.iflytek.lynxiao.support.dto.export.ExportParamDTO;
import com.iflytek.lynxiao.support.dto.export.QueryDocListDTO;
import com.iflytek.lynxiao.support.service.export.ExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.EnableSkynetSwagger2;

import java.io.BufferedWriter;
import java.io.OutputStreamWriter;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "临时导出需求工具")
@RestController
@RequestMapping("/api/v1/export/")
@EnableSkynetSwagger2
@Deprecated
public class ExportController {


    @Autowired
    private ExportService exportService;

    @Operation(summary = "导出query")
    @PostMapping("exportQuery")
    public void exportQuery(@RequestBody ExportParamDTO dto, HttpServletResponse response) {

        String filename = "ModelOutputByResponse_" + dto.getDate() + ".txt";
        response.setContentType("text/plain");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");

        List<ExportDTO> exportDTOS = exportService.exportQuery(dto);
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(response.getOutputStream()))) {
            for (ExportDTO obj : exportDTOS) {
                String line = obj.getQuery();
                writer.write(line);
                writer.newLine();
            }
        } catch (Exception e) {
            // 处理异常
            log.error(e.getMessage(), e);
        }
    }


    @Operation(summary = "导出query和doc列表")
    @PostMapping("exportQueryDoc")
    public void exportQueryDoc(@RequestBody ExportParamDTO dto, HttpServletResponse response) {

        String filename = "ModelOutputByResponse_" + dto.getDate() + ".txt";
        response.setContentType("text/plain");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");

        List<QueryDocListDTO> exportList = exportService.exportQueryDocList(dto);
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(response.getOutputStream()))) {
            for (QueryDocListDTO obj : exportList) {
                String line = JSONObject.toJSONString(obj);
                writer.write(line);
                writer.newLine();
            }
        } catch (Exception e) {
            // 处理异常
            log.error(e.getMessage(), e);
        }
    }
}
