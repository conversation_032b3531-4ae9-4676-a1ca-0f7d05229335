<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-pandora-starter-parent</artifactId>
        <version>1.3.3-SNAPSHOT</version>
    </parent>

    <groupId>com.iflytek.lynxiao</groupId>
    <artifactId>lynxiao-platform-support</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <skipTests>true</skipTests>
        <maven.deploy.skip>true</maven.deploy.skip>
        <skybox-contract.version>1.4.6-SNAPSHOT</skybox-contract.version>
        <turing-gateway-feign.version>*******</turing-gateway-feign.version>
        <commons-pool2.version>2.5.0</commons-pool2.version>
        <elasticsearch.version>7.15.2</elasticsearch.version>
        <alibaba-excel.version>3.3.2</alibaba-excel.version>
    </properties>

    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
        </repository>
    </repositories>

    <dependencies>
        <!-- Skynet Framework -->
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-pandora-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-pandora-ogma</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-mysql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-mongo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-boot-starter-metrics</artifactId>
        </dependency>

        <!-- Spring Boot/Cloud -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- Third Party -->
        <dependency>
            <groupId>com.iflytek.skybox</groupId>
            <artifactId>skybox-contract</artifactId>
            <version>${skybox-contract.version}</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.turing</groupId>
            <artifactId>turing-gateway-feign</artifactId>
            <version>${turing-gateway-feign.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>jakarta.persistence</groupId>
                    <artifactId>persistence-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${alibaba-excel.version}</version>
        </dependency>

        <!-- Elasticsearch -->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>${elasticsearch.version}</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${elasticsearch.version}</version>
        </dependency>

        <!-- JFreeChart 依赖，用于生成折线图 -->
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>1.5.3</version>
        </dependency>
        <!-- JCommon 依赖，JFreeChart 的依赖库 -->
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jcommon</artifactId>
            <version>1.0.24</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}_${buildSid}</finalName>
        <plugins>
            <plugin>
                <groupId>com.iflytek.skynet</groupId>
                <artifactId>skynet-boot-maven-plugin</artifactId>
                <version>${skynet-boot.version}</version>
                <configuration>
                    <moduleName>.</moduleName>
                    <packageName>com.iflytek.lynxiao.support</packageName>
                    <dbType>mysql</dbType>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>